--!strict
--[[
	GetCrateOdds RemoteFunction
	Retrieves crate odds information for transparency
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteFunction
local GetCrateOdds = Instance.new("RemoteFunction")
GetCrateOdds.Name = "GetCrateOdds"
GetCrateOdds.Parent = script.Parent

-- Type definitions for the function data
export type CrateOddsRequest = {
	CrateId: string,
	IncludePityInfo: boolean?
}

export type ItemOdds = {
	ItemId: string,
	ItemName: string,
	ItemType: string,
	Rarity: string,
	Odds: number, -- Percentage
	PityAdjustedOdds: number? -- If pity system applies
}

export type CrateOddsResponse = {
	Success: boolean,
	CrateId: string?,
	CrateName: string?,
	Items: {ItemOdds}?,
	RarityOdds: {[string]: number}?, -- Odds by rarity
	PityInfo: {
		CurrentCounter: number?,
		Threshold: number?,
		Increment: number?
	}?,
	Error: string?
}

return GetCrateOdds
