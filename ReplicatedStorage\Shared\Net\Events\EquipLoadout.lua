--!strict
--[[
	EquipLoadout RemoteEvent
	Handles player loadout changes with validation
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local EquipLoadout = Instance.new("RemoteEvent")
EquipLoadout.Name = "EquipLoadout"
EquipLoadout.Parent = script.Parent

-- Type definitions for the event data
export type EquipLoadoutData = {
	LoadoutSlot: string, -- "Primary", "Secondary", etc.
	WeaponId: string?,
	Attachments: {[string]: string}?,
	SkinId: string?
}

return EquipLoadout
