--!strict
--[[
	ProfileService - Player Data Management
	Handles player profile loading, saving, and session management with safety measures
]]

local Players = game:GetService("Players")
local DataStoreService = game:GetService("DataStoreService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

local Types = require(ReplicatedStorage.Shared.Types)
local Constants = require(ReplicatedStorage.Shared.Constants)
local Promise = require(ReplicatedStorage.Shared.Util.Promise)

local ProfileService = {}

-- Data store configuration
local PROFILE_STORE_NAME = "PlayerProfiles_v" .. Constants.DATA_VERSION
local SESSION_LOCK_TIMEOUT = 300 -- 5 minutes
local SAVE_INTERVAL = 60 -- Save every minute
local MAX_RETRIES = 3
local RETRY_DELAY = 2

-- In-memory profile storage
local loadedProfiles: {[number]: Types.PlayerProfile} = {}
local sessionLocks: {[number]: number} = {}
local saveQueue: {[number]: boolean} = {}

-- Data store instances
local profileStore = DataStoreService:GetDataStore(PROFILE_STORE_NAME)
local sessionStore = DataStoreService:GetDataStore("SessionLocks_v1")

-- Default profile template
local function createDefaultProfile(userId: number): Types.PlayerProfile
	return {
		UserId = userId,
		Level = 1,
		XP = 0,
		Credits = 1000, -- Starting credits
		Keys = 0,
		Shards = 0,
		UnlockedGuns = {Glock = true}, -- Start with basic pistol
		OwnedSkins = {},
		Loadouts = {
			Primary = {Primary = nil, Secondary = "Glock", Attachments = {}, Skin = nil},
			Secondary = {Primary = nil, Secondary = "Glock", Attachments = {}, Skin = nil}
		},
		MMRData = {
			Ranked1v1 = {
				Rating = Constants.DEFAULT_MMR.Rating,
				Deviation = Constants.DEFAULT_MMR.Deviation,
				Volatility = Constants.DEFAULT_MMR.Volatility,
				GamesPlayed = 0,
				Wins = 0,
				Losses = 0,
				Rank = "Tin",
				Division = 1
			},
			Ranked2v2 = {
				Rating = Constants.DEFAULT_MMR.Rating,
				Deviation = Constants.DEFAULT_MMR.Deviation,
				Volatility = Constants.DEFAULT_MMR.Volatility,
				GamesPlayed = 0,
				Wins = 0,
				Losses = 0,
				Rank = "Tin",
				Division = 1
			}
		},
		SeasonHistory = {},
		Settings = {
			Sensitivity = Constants.UI.DEFAULT_SENSITIVITY,
			FOV = Constants.UI.DEFAULT_FOV,
			ShowFPS = false,
			ShowPing = true,
			CrosshairStyle = "Cross",
			CrosshairColor = Constants.UI.DEFAULT_CROSSHAIR_COLOR,
			MasterVolume = Constants.AUDIO.DEFAULT_MASTER_VOLUME,
			SFXVolume = Constants.AUDIO.DEFAULT_SFX_VOLUME,
			MusicVolume = Constants.AUDIO.DEFAULT_MUSIC_VOLUME,
			Region = "NAE",
			AutoQueue = false
		},
		Statistics = {
			TotalKills = 0,
			TotalDeaths = 0,
			TotalDamage = 0,
			TotalHeadshots = 0,
			TotalRoundsPlayed = 0,
			TotalMatchesPlayed = 0,
			TotalPlaytime = 0,
			WeaponStats = {}
		},
		CreatedAt = os.time(),
		LastPlayed = os.time(),
		Version = Constants.DATA_VERSION
	}
end

-- Session lock management
local function acquireSessionLock(userId: number): Promise.Promise<boolean>
	return Promise.new(function(resolve, reject)
		local lockKey = "Lock_" .. userId
		local currentTime = os.time()
		
		-- Try to acquire lock
		local success, result = pcall(function()
			return sessionStore:UpdateAsync(lockKey, function(oldValue)
				local lockData = oldValue or {locked = false, timestamp = 0}
				
				-- Check if lock is expired
				if lockData.locked and (currentTime - lockData.timestamp) > SESSION_LOCK_TIMEOUT then
					lockData.locked = false
				end
				
				-- Try to acquire lock
				if not lockData.locked then
					return {locked = true, timestamp = currentTime}
				else
					return nil -- Lock is held by another session
				end
			end)
		end)
		
		if success and result then
			sessionLocks[userId] = currentTime
			resolve(true)
		else
			resolve(false)
		end
	end)
end

local function releaseSessionLock(userId: number): Promise.Promise<boolean>
	return Promise.new(function(resolve, reject)
		local lockKey = "Lock_" .. userId
		
		local success, result = pcall(function()
			return sessionStore:UpdateAsync(lockKey, function(oldValue)
				return {locked = false, timestamp = 0}
			end)
		end)
		
		sessionLocks[userId] = nil
		resolve(success)
	end)
end

-- Profile loading with retries
local function loadProfileData(userId: number): Promise.Promise<Types.PlayerProfile?>
	return Promise.new(function(resolve, reject)
		local function attemptLoad(retryCount: number)
			local success, result = pcall(function()
				return profileStore:GetAsync("Profile_" .. userId)
			end)
			
			if success then
				resolve(result)
			elseif retryCount < MAX_RETRIES then
				task.wait(RETRY_DELAY)
				attemptLoad(retryCount + 1)
			else
				reject("Failed to load profile after " .. MAX_RETRIES .. " attempts")
			end
		end
		
		attemptLoad(0)
	end)
end

-- Profile saving with retries
local function saveProfileData(userId: number, profile: Types.PlayerProfile): Promise.Promise<boolean>
	return Promise.new(function(resolve, reject)
		local function attemptSave(retryCount: number)
			local success, result = pcall(function()
				return profileStore:SetAsync("Profile_" .. userId, profile)
			end)
			
			if success then
				resolve(true)
			elseif retryCount < MAX_RETRIES then
				task.wait(RETRY_DELAY)
				attemptSave(retryCount + 1)
			else
				reject("Failed to save profile after " .. MAX_RETRIES .. " attempts")
			end
		end
		
		attemptSave(0)
	end)
end

-- Public API
function ProfileService.LoadProfile(userId: number): Promise.Promise<Types.PlayerProfile?>
	return Promise.new(function(resolve, reject)
		-- Check if already loaded
		if loadedProfiles[userId] then
			resolve(loadedProfiles[userId])
			return
		end
		
		-- Acquire session lock
		acquireSessionLock(userId):Then(function(lockAcquired)
			if not lockAcquired then
				reject("Could not acquire session lock")
				return
			end
			
			-- Load profile data
			loadProfileData(userId):Then(function(profileData)
				local profile = profileData or createDefaultProfile(userId)
				
				-- Migrate profile if needed
				if profile.Version < Constants.DATA_VERSION then
					profile = ProfileService.MigrateProfile(profile)
				end
				
				-- Update last played time
				profile.LastPlayed = os.time()
				
				-- Store in memory
				loadedProfiles[userId] = profile
				
				resolve(profile)
			end):Catch(function(error)
				releaseSessionLock(userId)
				reject(error)
			end)
		end):Catch(function(error)
			reject(error)
		end)
	end)
end

function ProfileService.SaveProfile(userId: number): Promise.Promise<boolean>
	return Promise.new(function(resolve, reject)
		local profile = loadedProfiles[userId]
		if not profile then
			reject("Profile not loaded")
			return
		end
		
		-- Update last played time
		profile.LastPlayed = os.time()
		
		saveProfileData(userId, profile):Then(function(success)
			saveQueue[userId] = nil
			resolve(success)
		end):Catch(function(error)
			reject(error)
		end)
	end)
end

function ProfileService.UnloadProfile(userId: number): Promise.Promise<boolean>
	return Promise.new(function(resolve, reject)
		-- Save profile first
		if loadedProfiles[userId] then
			ProfileService.SaveProfile(userId):Then(function()
				-- Release session lock
				releaseSessionLock(userId):Then(function()
					-- Remove from memory
					loadedProfiles[userId] = nil
					saveQueue[userId] = nil
					resolve(true)
				end):Catch(function(error)
					warn("Failed to release session lock for user " .. userId .. ": " .. error)
					resolve(true) -- Still consider unload successful
				end)
			end):Catch(function(error)
				reject(error)
			end)
		else
			resolve(true)
		end
	end)
end

function ProfileService.GetProfile(userId: number): Types.PlayerProfile?
	return loadedProfiles[userId]
end

function ProfileService.UpdateProfile(userId: number, updateFunction: (Types.PlayerProfile) -> Types.PlayerProfile): boolean
	local profile = loadedProfiles[userId]
	if not profile then
		return false
	end
	
	local updatedProfile = updateFunction(profile)
	loadedProfiles[userId] = updatedProfile
	saveQueue[userId] = true
	
	return true
end

function ProfileService.MigrateProfile(profile: Types.PlayerProfile): Types.PlayerProfile
	-- Handle profile migrations between versions
	local migratedProfile = profile
	
	-- Add any missing fields from default profile
	local defaultProfile = createDefaultProfile(profile.UserId)
	
	for key, value in pairs(defaultProfile) do
		if migratedProfile[key] == nil then
			migratedProfile[key] = value
		end
	end
	
	-- Update version
	migratedProfile.Version = Constants.DATA_VERSION
	
	return migratedProfile
end

-- Auto-save system
local function autoSaveProfiles()
	for userId, needsSave in pairs(saveQueue) do
		if needsSave then
			ProfileService.SaveProfile(userId):Catch(function(error)
				warn("Auto-save failed for user " .. userId .. ": " .. error)
			end)
		end
	end
end

-- Player connection handlers
local function onPlayerAdded(player: Player)
	ProfileService.LoadProfile(player.UserId):Catch(function(error)
		warn("Failed to load profile for " .. player.Name .. ": " .. error)
		player:Kick("Failed to load player data. Please try again.")
	end)
end

local function onPlayerRemoving(player: Player)
	ProfileService.UnloadProfile(player.UserId):Catch(function(error)
		warn("Failed to unload profile for " .. player.Name .. ": " .. error)
	end)
end

-- Initialize service
function ProfileService.Init()
	-- Connect player events
	Players.PlayerAdded:Connect(onPlayerAdded)
	Players.PlayerRemoving:Connect(onPlayerRemoving)
	
	-- Handle players already in game
	for _, player in ipairs(Players:GetPlayers()) do
		task.spawn(onPlayerAdded, player)
	end
	
	-- Start auto-save loop
	task.spawn(function()
		while true do
			task.wait(SAVE_INTERVAL)
			autoSaveProfiles()
		end
	end)
	
	print("ProfileService initialized")
end

-- Shutdown handler
game:BindToClose(function()
	print("Saving all profiles before shutdown...")
	
	local savePromises = {}
	for userId in pairs(loadedProfiles) do
		table.insert(savePromises, ProfileService.UnloadProfile(userId))
	end
	
	-- Wait for all saves to complete (with timeout)
	local allSaved = Promise.all(savePromises)
	local timeout = Promise.new(function(resolve)
		task.wait(10) -- 10 second timeout
		resolve(false)
	end)
	
	Promise.race({allSaved, timeout}):Await()
	print("Profile save complete")
end)

return ProfileService
