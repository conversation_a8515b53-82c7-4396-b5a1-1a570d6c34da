--!strict
--[[
	Arena Ascend - Type Definitions
	Centralized type definitions for the entire game
]]

export type UserId = number
export type PlaceId = number
export type JobId = string

-- Player Profile Types
export type PlayerProfile = {
	UserId: UserId,
	Level: number,
	XP: number,
	Credits: number,
	Keys: number,
	Shards: number,
	UnlockedGuns: {[string]: boolean},
	OwnedSkins: {[string]: boolean},
	Loadouts: {[string]: Loadout},
	MMRData: {[string]: MMREntry},
	SeasonHistory: {SeasonRecord},
	Settings: PlayerSettings,
	Statistics: PlayerStatistics,
	CreatedAt: number,
	LastPlayed: number,
	Version: number
}

export type MMREntry = {
	Rating: number,
	Deviation: number,
	Volatility: number,
	GamesPlayed: number,
	Wins: number,
	Losses: number,
	Rank: string,
	Division: number
}

export type SeasonRecord = {
	Season: number,
	FinalMMR: {[string]: number},
	FinalRank: {[string]: string},
	GamesPlayed: {[string]: number},
	Wins: {[string]: number},
	Losses: {[string]: number}
}

export type PlayerSettings = {
	Sensitivity: number,
	FOV: number,
	ShowFPS: boolean,
	ShowPing: boolean,
	CrosshairStyle: string,
	CrosshairColor: Color3,
	MasterVolume: number,
	SFXVolume: number,
	MusicVolume: number,
	Region: string,
	AutoQueue: boolean
}

export type PlayerStatistics = {
	TotalKills: number,
	TotalDeaths: number,
	TotalDamage: number,
	TotalHeadshots: number,
	TotalRoundsPlayed: number,
	TotalMatchesPlayed: number,
	TotalPlaytime: number,
	WeaponStats: {[string]: WeaponStatistics}
}

export type WeaponStatistics = {
	Kills: number,
	Shots: number,
	Hits: number,
	Headshots: number,
	Damage: number
}

-- Loadout Types
export type Loadout = {
	Primary: string?,
	Secondary: string?,
	Attachments: {[string]: string},
	Skin: string?
}

-- Weapon Types
export type WeaponData = {
	Name: string,
	Class: string,
	Type: "Hitscan" | "Projectile",
	Damage: {Head: number, Body: number, Limb: number},
	Range: {Min: number, Max: number},
	Falloff: {Start: number, End: number},
	RateOfFire: number,
	MagazineSize: number,
	ReloadTime: number,
	Spread: {Base: number, Moving: number, Max: number, Recovery: number},
	Recoil: {
		Pattern: {{X: number, Y: number}},
		Multiplier: number,
		Recovery: number
	},
	Movement: {
		WalkSpeed: number,
		AimSpeed: number
	},
	Effects: {
		MuzzleFlash: string?,
		ImpactEffect: string?,
		TracerEffect: string?,
		Sound: string?
	},
	UnlockLevel: number,
	RankedAllowed: boolean
}

-- Match Types
export type MatchConfig = {
	Mode: "Ranked1v1" | "Ranked2v2" | "Casual",
	Map: string,
	RoundsToWin: number,
	RoundTime: number,
	OvertimeEnabled: boolean,
	TeamSize: number,
	AllowedWeapons: {string}?,
	FriendlyFire: boolean,
	Economy: boolean
}

export type MatchResult = {
	MatchId: string,
	Mode: string,
	Map: string,
	Players: {MatchPlayer},
	Teams: {MatchTeam},
	Rounds: {RoundResult},
	Duration: number,
	CompletedAt: number
}

export type MatchPlayer = {
	UserId: UserId,
	Team: number,
	Kills: number,
	Deaths: number,
	Damage: number,
	Headshots: number,
	MMRBefore: number?,
	MMRAfter: number?,
	XPGained: number,
	CreditsGained: number
}

export type MatchTeam = {
	TeamId: number,
	Score: number,
	Players: {UserId}
}

export type RoundResult = {
	RoundNumber: number,
	Winner: number,
	Duration: number,
	PlayerStats: {[UserId]: RoundPlayerStats}
}

export type RoundPlayerStats = {
	Kills: number,
	Deaths: number,
	Damage: number,
	Headshots: number
}

-- Queue Types
export type QueueTicket = {
	TicketId: string,
	UserId: UserId,
	PartyMembers: {UserId}?,
	Mode: string,
	Region: string,
	MMR: number,
	CreatedAt: number,
	Preferences: QueuePreferences?
}

export type QueuePreferences = {
	MaxPing: number?,
	PreferredMaps: {string}?
}

export type MatchmadeGroup = {
	Players: {QueueTicket},
	AverageMMR: number,
	Region: string,
	Mode: string
}

-- Crate Types
export type CrateData = {
	Id: string,
	Name: string,
	Description: string,
	KeyRequired: boolean,
	CreditCost: number?,
	Items: {CrateItem},
	PityCounter: number,
	PityThreshold: number,
	Icon: string
}

export type CrateItem = {
	ItemId: string,
	ItemType: "Skin" | "Banner" | "Pose" | "Effect",
	Rarity: "Common" | "Uncommon" | "Rare" | "Epic" | "Legendary",
	Weight: number,
	ShardValue: number
}

export type CrateResult = {
	ItemId: string,
	ItemType: string,
	Rarity: string,
	IsNew: boolean,
	ShardsAwarded: number?
}

-- Network Types
export type RemoteEventData = {
	[string]: any
}

export type ValidationResult = {
	Success: boolean,
	Error: string?
}

-- Combat Types
export type HitData = {
	Weapon: string,
	Origin: Vector3,
	Direction: Vector3,
	Distance: number,
	HitPart: string?,
	Damage: number,
	Timestamp: number
}

export type FireRequest = {
	Weapon: string,
	Origin: Vector3,
	Direction: Vector3,
	Timestamp: number
}

return {}
