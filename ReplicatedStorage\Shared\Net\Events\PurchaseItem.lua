--!strict
--[[
	PurchaseItem RemoteEvent
	Handles item purchase requests with validation
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local PurchaseItem = Instance.new("RemoteEvent")
PurchaseItem.Name = "PurchaseItem"
PurchaseItem.Parent = script.Parent

-- Type definitions for the event data
export type PurchaseItemData = {
	ItemId: string,
	ItemType: "Weapon" | "Skin" | "Crate" | "Key" | "Utility",
	Currency: "Credits" | "Keys" | "Shards" | "Robux",
	Quantity: number?
}

export type PurchaseResultData = {
	Success: boolean,
	ItemsReceived: {string}?,
	CurrencySpent: {[string]: number}?,
	Error: string?
}

return PurchaseItem
