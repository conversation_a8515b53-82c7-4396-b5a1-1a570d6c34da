--!strict
--[[
	EconomyConfig - Game Economy Configuration
	Defines XP, credits, progression, and monetization parameters
]]

local EconomyConfig = {}

-- Currency Configuration
EconomyConfig.CURRENCIES = {
	Credits = {
		Name = "Credits",
		Description = "Earned through gameplay",
		Icon = "rbxassetid://0",
		Color = Color3.fromRGB(255, 215, 0),
		Earnable = true,
		Purchasable = false
	},
	Keys = {
		Name = "Keys",
		Description = "Premium currency for crates",
		Icon = "rbxassetid://0",
		Color = Color3.fromRGB(0, 162, 255),
		Earnable = false,
		Purchasable = true
	},
	Shards = {
		Name = "Shards",
		Description = "Crafting material from duplicates",
		Icon = "rbxassetid://0",
		Color = Color3.fromRGB(138, 43, 226),
		Earnable = true,
		Purchasable = false
	}
}

-- XP and Leveling System
EconomyConfig.PROGRESSION = {
	-- XP Sources
	XP_SOURCES = {
		Kill = 100,
		Headshot = 150,
		RoundWin = 200,
		MatchWin = 500,
		FirstWinOfDay = 1000,
		RankedWin = 750,
		Assist = 50,
		PlantDefuse = 100, -- For future bomb modes
		Clutch = 300, -- 1vX situations
		Ace = 500, -- 5 kills in one round
		Comeback = 250 -- Winning from behind
	},
	
	-- Level Requirements
	BASE_XP_REQUIREMENT = 1000,
	XP_SCALING_FACTOR = 1.1, -- Each level requires 10% more XP
	MAX_LEVEL = 100,
	
	-- Level Rewards
	LEVEL_REWARDS = {
		{Level = 5, Reward = {Type = "Credits", Amount = 500}},
		{Level = 10, Reward = {Type = "Weapon", Item = "M4A1"}},
		{Level = 15, Reward = {Type = "Credits", Amount = 1000}},
		{Level = 20, Reward = {Type = "Weapon", Item = "AK47"}},
		{Level = 25, Reward = {Type = "Crate", Item = "StandardCrate"}},
		{Level = 30, Reward = {Type = "Credits", Amount = 2000}},
		{Level = 35, Reward = {Type = "Weapon", Item = "AWP"}},
		{Level = 40, Reward = {Type = "Crate", Item = "PremiumCrate"}},
		{Level = 50, Reward = {Type = "Credits", Amount = 5000}},
		{Level = 75, Reward = {Type = "Crate", Item = "EliteCrate"}},
		{Level = 100, Reward = {Type = "Credits", Amount = 10000}}
	}
}

-- Credit Economy
EconomyConfig.CREDITS = {
	-- Earning Sources
	EARN_SOURCES = {
		Kill = 10,
		Headshot = 15,
		RoundWin = 25,
		MatchWin = 100,
		RankedWin = 150,
		DailyBonus = 500,
		WeeklyBonus = 2000,
		FirstWinOfDay = 200,
		Assist = 5,
		Clutch = 50,
		Ace = 100
	},
	
	-- Daily/Weekly Missions
	DAILY_MISSIONS = {
		{
			Id = "daily_kills",
			Name = "Get 10 kills",
			Description = "Eliminate 10 enemies in any mode",
			Target = 10,
			Reward = {Type = "Credits", Amount = 200},
			ResetDaily = true
		},
		{
			Id = "daily_wins",
			Name = "Win 3 matches",
			Description = "Win 3 matches in any mode",
			Target = 3,
			Reward = {Type = "Credits", Amount = 300},
			ResetDaily = true
		},
		{
			Id = "daily_headshots",
			Name = "Get 5 headshots",
			Description = "Score 5 headshot kills",
			Target = 5,
			Reward = {Type = "Credits", Amount = 250},
			ResetDaily = true
		}
	},
	
	WEEKLY_MISSIONS = {
		{
			Id = "weekly_ranked",
			Name = "Play 10 ranked matches",
			Description = "Complete 10 ranked matches",
			Target = 10,
			Reward = {Type = "Credits", Amount = 1000},
			ResetWeekly = true
		},
		{
			Id = "weekly_damage",
			Name = "Deal 5000 damage",
			Description = "Deal 5000 total damage",
			Target = 5000,
			Reward = {Type = "Credits", Amount = 800},
			ResetWeekly = true
		}
	},
	
	-- Spending
	SHOP_ITEMS = {
		{
			Id = "standard_crate",
			Name = "Standard Crate",
			Price = 1000,
			Currency = "Credits",
			Category = "Crates"
		},
		{
			Id = "premium_crate",
			Name = "Premium Crate",
			Price = 2500,
			Currency = "Credits",
			Category = "Crates"
		},
		{
			Id = "name_change",
			Name = "Display Name Change",
			Price = 5000,
			Currency = "Credits",
			Category = "Utility"
		}
	}
}

-- Key Economy (Premium Currency)
EconomyConfig.KEYS = {
	-- Robux Packages
	ROBUX_PACKAGES = {
		{
			Id = "keys_small",
			Name = "5 Keys",
			Keys = 5,
			RobuxPrice = 100,
			BonusKeys = 0,
			Popular = false
		},
		{
			Id = "keys_medium",
			Name = "12 Keys",
			Keys = 10,
			RobuxPrice = 200,
			BonusKeys = 2,
			Popular = true
		},
		{
			Id = "keys_large",
			Name = "25 Keys",
			Keys = 20,
			RobuxPrice = 400,
			BonusKeys = 5,
			Popular = false
		},
		{
			Id = "keys_mega",
			Name = "60 Keys",
			Keys = 50,
			RobuxPrice = 800,
			BonusKeys = 10,
			Popular = false
		}
	},
	
	-- Key Uses
	KEY_USES = {
		{Item = "PremiumCrate", KeysRequired = 1},
		{Item = "EliteCrate", KeysRequired = 2},
		{Item = "LegendaryCrate", KeysRequired = 3}
	}
}

-- Shard Economy
EconomyConfig.SHARDS = {
	-- Conversion Rates (from duplicates)
	CONVERSION_RATES = {
		Common = 5,
		Uncommon = 15,
		Rare = 50,
		Epic = 150,
		Legendary = 500
	},
	
	-- Crafting Costs
	CRAFTING_COSTS = {
		Common = 25,
		Uncommon = 75,
		Rare = 250,
		Epic = 750,
		Legendary = 2500
	},
	
	-- Special Items
	SPECIAL_ITEMS = {
		{
			Id = "golden_ak47",
			Name = "Golden AK-47",
			ShardCost = 5000,
			Rarity = "Legendary",
			Limited = true
		},
		{
			Id = "diamond_awp",
			Name = "Diamond AWP",
			ShardCost = 7500,
			Rarity = "Legendary",
			Limited = true
		}
	}
}

-- Battle Pass System
EconomyConfig.BATTLE_PASS = {
	ENABLED = true,
	SEASON_LENGTH_DAYS = 90,
	
	-- Tiers
	MAX_TIER = 100,
	XP_PER_TIER = 1000,
	
	-- Premium Pass
	PREMIUM_PASS_PRICE = 1000, -- Robux
	PREMIUM_BENEFITS = {
		"Double XP weekends",
		"Exclusive skins",
		"Premium crate discounts",
		"Early access to new weapons"
	},
	
	-- Rewards Structure
	FREE_TIER_REWARDS = {
		{Tier = 1, Reward = {Type = "Credits", Amount = 100}},
		{Tier = 5, Reward = {Type = "Weapon", Item = "Glock"}},
		{Tier = 10, Reward = {Type = "Credits", Amount = 500}},
		{Tier = 15, Reward = {Type = "Skin", Item = "basic_m4_camo"}},
		{Tier = 20, Reward = {Type = "Credits", Amount = 1000}},
		-- Continue pattern...
	},
	
	PREMIUM_TIER_REWARDS = {
		{Tier = 1, Reward = {Type = "Credits", Amount = 200}},
		{Tier = 3, Reward = {Type = "Skin", Item = "premium_ak_skin"}},
		{Tier = 7, Reward = {Type = "Keys", Amount = 2}},
		{Tier = 12, Reward = {Type = "Skin", Item = "rare_awp_skin"}},
		-- Continue pattern...
	}
}

-- Anti-Exploitation Measures
EconomyConfig.ANTI_EXPLOIT = {
	-- Rate Limits
	MAX_CREDITS_PER_HOUR = 2000,
	MAX_XP_PER_HOUR = 5000,
	
	-- Validation
	VALIDATE_EARNINGS = true,
	MAX_KILL_STREAK_BONUS = 10, -- Cap bonus multipliers
	
	-- Suspicious Activity Detection
	DETECT_FARMING = true,
	FARMING_THRESHOLD = {
		KillsPerMinute = 5,
		CreditsPerMinute = 100,
		XPPerMinute = 200
	},
	
	-- Penalties
	FARMING_PENALTY = {
		FirstOffense = "Warning",
		SecondOffense = "24h earnings reduction",
		ThirdOffense = "7d earnings ban"
	}
}

-- Helper Functions
function EconomyConfig.GetXPRequiredForLevel(level: number): number
	if level <= 1 then
		return 0
	end
	
	local totalXP = 0
	for i = 2, level do
		local levelXP = math.floor(EconomyConfig.PROGRESSION.BASE_XP_REQUIREMENT * 
			(EconomyConfig.PROGRESSION.XP_SCALING_FACTOR ^ (i - 2)))
		totalXP += levelXP
	end
	
	return totalXP
end

function EconomyConfig.GetLevelFromXP(xp: number): number
	local level = 1
	local totalXP = 0
	
	while level < EconomyConfig.PROGRESSION.MAX_LEVEL do
		local nextLevelXP = EconomyConfig.GetXPRequiredForLevel(level + 1)
		if xp < nextLevelXP then
			break
		end
		level += 1
	end
	
	return level
end

function EconomyConfig.GetShardValue(rarity: string): number
	return EconomyConfig.SHARDS.CONVERSION_RATES[rarity] or 0
end

function EconomyConfig.GetCraftingCost(rarity: string): number
	return EconomyConfig.SHARDS.CRAFTING_COSTS[rarity] or 0
end

function EconomyConfig.ValidateEarnings(earnType: string, amount: number, timeWindow: number): boolean
	-- Implement earning validation logic
	local maxPerHour = EconomyConfig.ANTI_EXPLOIT.MAX_CREDITS_PER_HOUR
	if earnType == "XP" then
		maxPerHour = EconomyConfig.ANTI_EXPLOIT.MAX_XP_PER_HOUR
	end
	
	local hourlyRate = (amount / timeWindow) * 3600
	return hourlyRate <= maxPerHour
end

return EconomyConfig
