--!strict
--[[
	GetQueues RemoteFunction
	Retrieves current queue status and information
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteFunction
local GetQueues = Instance.new("RemoteFunction")
GetQueues.Name = "GetQueues"
GetQueues.Parent = script.Parent

-- Type definitions for the function data
export type QueueRequest = {
	Region: string?,
	Mode: string?
}

export type QueueInfo = {
	Mode: string,
	Region: string,
	PlayersInQueue: number,
	EstimatedWaitTime: number,
	Status: "Active" | "Disabled" | "Maintenance"
}

export type QueueResponse = {
	Success: boolean,
	Queues: {QueueInfo}?,
	PlayerQueueStatus: {
		InQueue: boolean,
		Mode: string?,
		Region: string?,
		WaitTime: number?,
		Position: number?
	}?,
	Error: string?
}

return GetQueues
