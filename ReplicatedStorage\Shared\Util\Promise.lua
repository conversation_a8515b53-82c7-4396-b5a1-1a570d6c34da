--!strict
--[[
	Promise - Lightweight Promise Implementation
	A simple Promise implementation for asynchronous operations
]]

local Promise = {}
Promise.__index = Promise

export type Promise<T> = {
	Then: (self: Promise<T>, onResolved: (T) -> (), onRejected: ((any) -> ())?) -> Promise<T>,
	Catch: (self: Promise<T>, onRejected: (any) -> ()) -> Promise<T>,
	Finally: (self: Promise<T>, onFinally: () -> ()) -> Promise<T>,
	Await: (self: Promise<T>) -> (boolean, T | any),
	Cancel: (self: Promise<T>) -> ()
}

type PromiseState = "Pending" | "Resolved" | "Rejected" | "Cancelled"

type PromiseImpl<T> = {
	_state: PromiseState,
	_value: T?,
	_reason: any?,
	_callbacks: {{
		onResolved: ((T) -> ())?,
		onRejected: ((any) -> ())?,
		onFinally: (() -> ())?
	}}
}

function Promise.new<T>(executor: (resolve: (T) -> (), reject: (any) -> ()) -> ()): Promise<T>
	local self: PromiseImpl<T> = {
		_state = "Pending",
		_value = nil,
		_reason = nil,
		_callbacks = {}
	}
	
	setmetatable(self, Promise)
	
	local function resolve(value: T)
		if self._state ~= "Pending" then
			return
		end
		
		self._state = "Resolved"
		self._value = value
		
		for _, callback in ipairs(self._callbacks) do
			if callback.onResolved then
				task.spawn(callback.onResolved, value)
			end
			if callback.onFinally then
				task.spawn(callback.onFinally)
			end
		end
		
		table.clear(self._callbacks)
	end
	
	local function reject(reason: any)
		if self._state ~= "Pending" then
			return
		end
		
		self._state = "Rejected"
		self._reason = reason
		
		for _, callback in ipairs(self._callbacks) do
			if callback.onRejected then
				task.spawn(callback.onRejected, reason)
			end
			if callback.onFinally then
				task.spawn(callback.onFinally)
			end
		end
		
		table.clear(self._callbacks)
	end
	
	task.spawn(function()
		local success, result = pcall(executor, resolve, reject)
		if not success then
			reject(result)
		end
	end)
	
	return self :: any
end

function Promise.resolve<T>(value: T): Promise<T>
	return Promise.new(function(resolve)
		resolve(value)
	end)
end

function Promise.reject<T>(reason: any): Promise<T>
	return Promise.new(function(_, reject)
		reject(reason)
	end)
end

function Promise.all<T>(promises: {Promise<T>}): Promise<{T}>
	return Promise.new(function(resolve, reject)
		if #promises == 0 then
			resolve({})
			return
		end
		
		local results = {}
		local completed = 0
		
		for i, promise in ipairs(promises) do
			promise:Then(function(value)
				results[i] = value
				completed += 1
				
				if completed == #promises then
					resolve(results)
				end
			end, reject)
		end
	end)
end

function Promise.race<T>(promises: {Promise<T>}): Promise<T>
	return Promise.new(function(resolve, reject)
		for _, promise in ipairs(promises) do
			promise:Then(resolve, reject)
		end
	end)
end

function Promise:Then<T>(onResolved: (T) -> (), onRejected: ((any) -> ())?): Promise<T>
	if self._state == "Resolved" and onResolved then
		task.spawn(onResolved, self._value)
	elseif self._state == "Rejected" and onRejected then
		task.spawn(onRejected, self._reason)
	elseif self._state == "Pending" then
		table.insert(self._callbacks, {
			onResolved = onResolved,
			onRejected = onRejected
		})
	end
	
	return self
end

function Promise:Catch<T>(onRejected: (any) -> ()): Promise<T>
	return self:Then(nil, onRejected)
end

function Promise:Finally<T>(onFinally: () -> ()): Promise<T>
	if self._state ~= "Pending" then
		task.spawn(onFinally)
	else
		table.insert(self._callbacks, {
			onFinally = onFinally
		})
	end
	
	return self
end

function Promise:Await<T>(): (boolean, T | any)
	if self._state == "Resolved" then
		return true, self._value
	elseif self._state == "Rejected" then
		return false, self._reason
	elseif self._state == "Cancelled" then
		return false, "Promise was cancelled"
	end
	
	local thread = coroutine.running()
	
	self:Then(function(value)
		task.spawn(thread, true, value)
	end, function(reason)
		task.spawn(thread, false, reason)
	end)
	
	return coroutine.yield()
end

function Promise:Cancel<T>()
	if self._state ~= "Pending" then
		return
	end
	
	self._state = "Cancelled"
	table.clear(self._callbacks)
end

return Promise
