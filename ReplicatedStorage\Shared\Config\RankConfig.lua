--!strict
--[[
	RankConfig - Ranking System Configuration
	Defines rank tiers, MMR thresholds, and visual assets
]]

local RankConfig = {}

-- Rank Definitions
RankConfig.RANKS = {
	{
		Name = "Tin",
		Divisions = 3,
		MinMMR = 0,
		MaxMMR = 399,
		Color = Color3.fromRGB(139, 69, 19),
		Icon = "rbxassetid://0", -- Replace with actual asset ID
		Description = "Starting rank for new players"
	},
	{
		Name = "Bronze",
		Divisions = 3,
		MinMMR = 400,
		MaxMMR = 799,
		Color = Color3.fromRGB(205, 127, 50),
		Icon = "rbxassetid://0",
		Description = "Basic competitive understanding"
	},
	{
		Name = "Silver",
		Divisions = 3,
		MinMMR = 800,
		MaxMMR = 1199,
		Color = Color3.fromRGB(192, 192, 192),
		Icon = "rbxassetid://0",
		Description = "Developing game sense"
	},
	{
		Name = "Gold",
		Divisions = 3,
		MinMMR = 1200,
		MaxMMR = 1599,
		Color = Color3.fromRGB(255, 215, 0),
		Icon = "rbxassetid://0",
		Description = "Above average skill level"
	},
	{
		Name = "Platinum",
		Divisions = 3,
		MinMMR = 1600,
		MaxMMR = 1999,
		Color = Color3.fromRGB(229, 228, 226),
		Icon = "rbxassetid://0",
		Description = "High skill competitive play"
	},
	{
		Name = "Diamond",
		Divisions = 3,
		MinMMR = 2000,
		MaxMMR = 2399,
		Color = Color3.fromRGB(185, 242, 255),
		Icon = "rbxassetid://0",
		Description = "Elite level gameplay"
	},
	{
		Name = "Onyx",
		Divisions = 3,
		MinMMR = 2400,
		MaxMMR = 2799,
		Color = Color3.fromRGB(53, 56, 57),
		Icon = "rbxassetid://0",
		Description = "Master tier competition"
	},
	{
		Name = "Ascendant",
		Divisions = 3,
		MinMMR = 2800,
		MaxMMR = 3199,
		Color = Color3.fromRGB(138, 43, 226),
		Icon = "rbxassetid://0",
		Description = "Near-professional level"
	},
	{
		Name = "Apex",
		Divisions = 3,
		MinMMR = 3200,
		MaxMMR = 3599,
		Color = Color3.fromRGB(255, 20, 147),
		Icon = "rbxassetid://0",
		Description = "Peak competitive performance"
	},
	{
		Name = "Top 100",
		Divisions = 1,
		MinMMR = 3600,
		MaxMMR = math.huge,
		Color = Color3.fromRGB(255, 255, 255),
		Icon = "rbxassetid://0",
		Description = "Global leaderboard elite"
	}
}

-- Division Names
RankConfig.DIVISION_NAMES = {"III", "II", "I"}

-- MMR System Configuration
RankConfig.MMR_CONFIG = {
	-- Glicko-2 Parameters
	DEFAULT_RATING = 1500,
	DEFAULT_DEVIATION = 350,
	DEFAULT_VOLATILITY = 0.06,
	
	-- System Constants
	TAU = 0.5, -- System volatility constraint
	EPSILON = 0.000001, -- Convergence tolerance
	
	-- K-Factor adjustments
	PLACEMENT_MULTIPLIER = 2.0, -- Extra MMR gain/loss during placement
	PROVISIONAL_GAMES = 10, -- Games before rating stabilizes
	
	-- Rating bounds
	MIN_RATING = 0,
	MAX_RATING = 4000,
	MIN_DEVIATION = 30,
	MAX_DEVIATION = 350,
	
	-- Decay system
	DECAY_ENABLED = true,
	DECAY_DAYS = 14, -- Days of inactivity before decay starts
	DECAY_RATE = 0.02, -- Percentage lost per day
	MIN_DECAY_RATING = 1200 -- Don't decay below this rating
}

-- Seasonal Configuration
RankConfig.SEASON_CONFIG = {
	CURRENT_SEASON = 1,
	SEASON_LENGTH_DAYS = 90,
	
	-- Reset behavior
	SOFT_RESET_ENABLED = true,
	RESET_FACTOR = 0.8, -- Retain 80% of previous season MMR
	RESET_FLOOR = 800, -- Minimum MMR after reset
	RESET_CEILING = 2400, -- Maximum MMR after reset
	
	-- Placement matches
	PLACEMENT_MATCHES = 10,
	PLACEMENT_UNCERTAINTY = 100, -- Extra RD during placements
	
	-- Season rewards
	SEASON_REWARDS = {
		{MinRank = "Bronze", Reward = {Type = "Credits", Amount = 500}},
		{MinRank = "Silver", Reward = {Type = "Credits", Amount = 1000}},
		{MinRank = "Gold", Reward = {Type = "Credits", Amount = 2000}},
		{MinRank = "Platinum", Reward = {Type = "Credits", Amount = 3500}},
		{MinRank = "Diamond", Reward = {Type = "Credits", Amount = 5000}},
		{MinRank = "Onyx", Reward = {Type = "Credits", Amount = 7500}},
		{MinRank = "Ascendant", Reward = {Type = "Credits", Amount = 10000}},
		{MinRank = "Apex", Reward = {Type = "Credits", Amount = 15000}},
		{MinRank = "Top 100", Reward = {Type = "Credits", Amount = 25000}}
	}
}

-- Leaderboard Configuration
RankConfig.LEADERBOARD_CONFIG = {
	TOP_PLAYERS_COUNT = 100,
	UPDATE_INTERVAL = 300, -- 5 minutes
	MINIMUM_GAMES = 20, -- Minimum games to appear on leaderboard
	REGIONS = {"Global", "OCE", "NAE", "EU"}
}

-- Rank Display Configuration
RankConfig.DISPLAY_CONFIG = {
	SHOW_MMR = false, -- Hide exact MMR from players
	SHOW_DIVISION = true,
	SHOW_PROGRESS = true, -- Show progress to next division
	
	-- Animation settings
	RANK_UP_ANIMATION_TIME = 2.0,
	RANK_DOWN_ANIMATION_TIME = 1.5,
	
	-- Colors for UI elements
	RANK_UP_COLOR = Color3.fromRGB(0, 255, 0),
	RANK_DOWN_COLOR = Color3.fromRGB(255, 0, 0),
	NEUTRAL_COLOR = Color3.fromRGB(255, 255, 255)
}

-- Helper Functions
function RankConfig.GetRankFromMMR(mmr: number): (string, number)
	for _, rank in ipairs(RankConfig.RANKS) do
		if mmr >= rank.MinMMR and mmr <= rank.MaxMMR then
			if rank.Divisions == 1 then
				return rank.Name, 1
			else
				-- Calculate division within rank
				local rankRange = rank.MaxMMR - rank.MinMMR
				local divisionSize = rankRange / rank.Divisions
				local relativeMMR = mmr - rank.MinMMR
				local division = math.min(rank.Divisions, math.floor(relativeMMR / divisionSize) + 1)
				return rank.Name, division
			end
		end
	end
	
	-- Fallback to lowest rank
	return RankConfig.RANKS[1].Name, 1
end

function RankConfig.GetRankData(rankName: string): any?
	for _, rank in ipairs(RankConfig.RANKS) do
		if rank.Name == rankName then
			return rank
		end
	end
	return nil
end

function RankConfig.GetNextRank(currentRank: string, currentDivision: number): (string?, number?)
	local currentRankData = RankConfig.GetRankData(currentRank)
	if not currentRankData then
		return nil, nil
	end
	
	-- Check if we can promote within current rank
	if currentDivision < currentRankData.Divisions then
		return currentRank, currentDivision + 1
	end
	
	-- Find next rank
	for i, rank in ipairs(RankConfig.RANKS) do
		if rank.Name == currentRank and i < #RankConfig.RANKS then
			local nextRank = RankConfig.RANKS[i + 1]
			return nextRank.Name, 1
		end
	end
	
	return nil, nil
end

function RankConfig.GetPreviousRank(currentRank: string, currentDivision: number): (string?, number?)
	local currentRankData = RankConfig.GetRankData(currentRank)
	if not currentRankData then
		return nil, nil
	end
	
	-- Check if we can demote within current rank
	if currentDivision > 1 then
		return currentRank, currentDivision - 1
	end
	
	-- Find previous rank
	for i, rank in ipairs(RankConfig.RANKS) do
		if rank.Name == currentRank and i > 1 then
			local prevRank = RankConfig.RANKS[i - 1]
			return prevRank.Name, prevRank.Divisions
		end
	end
	
	return nil, nil
end

function RankConfig.GetMMRForRankDivision(rankName: string, division: number): number?
	local rankData = RankConfig.GetRankData(rankName)
	if not rankData then
		return nil
	end
	
	if rankData.Divisions == 1 then
		return rankData.MinMMR
	end
	
	local rankRange = rankData.MaxMMR - rankData.MinMMR
	local divisionSize = rankRange / rankData.Divisions
	return rankData.MinMMR + (division - 1) * divisionSize
end

return RankConfig
