--!strict
--[[
	Arena Ascend - One-Click Installer
	Run this script in ServerScriptService to automatically create the entire game structure
	
	INSTRUCTIONS:
	1. Create a ServerScript in ServerScriptService
	2. Copy this entire code into it
	3. Run the game (F5)
	4. The script will create everything automatically
	5. Delete this script after it runs successfully
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")
local StarterPlayer = game:GetService("StarterPlayer")
local StarterGui = game:GetService("StarterGui")
local ServerStorage = game:GetService("ServerStorage")

local ArenaAscendInstaller = {}

-- Utility function to create folder structure
local function createFolder(parent: Instance, name: string): Folder
	local existing = parent:FindFirstChild(name)
	if existing and existing:IsA("Folder") then
		return existing
	end
	
	local folder = Instance.new("Folder")
	folder.Name = name
	folder.Parent = parent
	return folder
end

-- Utility function to create ModuleScript with code
local function createModuleScript(parent: Instance, name: string, code: string): ModuleScript
	local existing = parent:FindFirstChild(name)
	if existing then
		existing:Destroy()
	end
	
	local module = Instance.new("ModuleScript")
	module.Name = name
	module.Source = code
	module.Parent = parent
	return module
end

-- Utility function to create ServerScript with code
local function createServerScript(parent: Instance, name: string, code: string): Script
	local existing = parent:FindFirstChild(name)
	if existing then
		existing:Destroy()
	end
	
	local script = Instance.new("Script")
	script.Name = name
	script.Source = code
	script.Parent = parent
	return script
end

-- Utility function to create RemoteEvent
local function createRemoteEvent(parent: Instance, name: string): RemoteEvent
	local existing = parent:FindFirstChild(name)
	if existing and existing:IsA("RemoteEvent") then
		return existing
	elseif existing then
		existing:Destroy()
	end
	
	local remote = Instance.new("RemoteEvent")
	remote.Name = name
	remote.Parent = parent
	return remote
end

-- Utility function to create RemoteFunction
local function createRemoteFunction(parent: Instance, name: string): RemoteFunction
	local existing = parent:FindFirstChild(name)
	if existing and existing:IsA("RemoteFunction") then
		return existing
	elseif existing then
		existing:Destroy()
	end
	
	local remote = Instance.new("RemoteFunction")
	remote.Name = name
	remote.Parent = parent
	return remote
end

function ArenaAscendInstaller.CreateFolderStructure()
	print("🏗️ Creating folder structure...")
	
	-- ReplicatedStorage structure
	local shared = createFolder(ReplicatedStorage, "Shared")
	local config = createFolder(shared, "Config")
	local util = createFolder(shared, "Util")
	local net = createFolder(shared, "Net")
	local events = createFolder(net, "Events")
	local functions = createFolder(net, "Functions")
	createFolder(ReplicatedStorage, "UIAssets")
	
	-- ServerScriptService structure
	local services = createFolder(ServerScriptService, "Services")
	
	-- StarterPlayer structure
	local starterScripts = StarterPlayer:FindFirstChild("StarterPlayerScripts") or createFolder(StarterPlayer, "StarterPlayerScripts")
	local controllers = createFolder(starterScripts, "Controllers")
	local state = createFolder(starterScripts, "State")
	createFolder(state, "Slices")
	
	-- StarterGui structure
	createFolder(StarterGui, "ScreenGuis")
	
	-- ServerStorage structure
	createFolder(ServerStorage, "Weapons")
	createFolder(ServerStorage, "Maps")
	createFolder(ServerStorage, "CrateRollVFX")
	
	print("✅ Folder structure created!")
	return {
		shared = shared,
		config = config,
		util = util,
		events = events,
		functions = functions,
		services = services,
		controllers = controllers,
		state = state
	}
end

function ArenaAscendInstaller.CreateCoreModules(folders)
	print("📝 Creating core modules...")
	
	-- Types.lua
	createModuleScript(folders.shared, "Types", [[--!strict
--[[
	Arena Ascend - Type Definitions
	Centralized type definitions for the entire game
]]

export type UserId = number
export type PlaceId = number
export type JobId = string

-- Player Profile Types
export type PlayerProfile = {
	UserId: UserId,
	Level: number,
	XP: number,
	Credits: number,
	Keys: number,
	Shards: number,
	UnlockedGuns: {[string]: boolean},
	OwnedSkins: {[string]: boolean},
	Loadouts: {[string]: Loadout},
	MMRData: {[string]: MMREntry},
	SeasonHistory: {SeasonRecord},
	Settings: PlayerSettings,
	Statistics: PlayerStatistics,
	CreatedAt: number,
	LastPlayed: number,
	Version: number
}

export type MMREntry = {
	Rating: number,
	Deviation: number,
	Volatility: number,
	GamesPlayed: number,
	Wins: number,
	Losses: number,
	Rank: string,
	Division: number
}

export type SeasonRecord = {
	Season: number,
	FinalMMR: {[string]: number},
	FinalRank: {[string]: string},
	GamesPlayed: {[string]: number},
	Wins: {[string]: number},
	Losses: {[string]: number}
}

export type PlayerSettings = {
	Sensitivity: number,
	FOV: number,
	ShowFPS: boolean,
	ShowPing: boolean,
	CrosshairStyle: string,
	CrosshairColor: Color3,
	MasterVolume: number,
	SFXVolume: number,
	MusicVolume: number,
	Region: string,
	AutoQueue: boolean
}

export type PlayerStatistics = {
	TotalKills: number,
	TotalDeaths: number,
	TotalDamage: number,
	TotalHeadshots: number,
	TotalRoundsPlayed: number,
	TotalMatchesPlayed: number,
	TotalPlaytime: number,
	WeaponStats: {[string]: WeaponStatistics}
}

export type WeaponStatistics = {
	Kills: number,
	Shots: number,
	Hits: number,
	Headshots: number,
	Damage: number
}

-- Loadout Types
export type Loadout = {
	Primary: string?,
	Secondary: string?,
	Attachments: {[string]: string},
	Skin: string?
}

-- Weapon Types
export type WeaponData = {
	Name: string,
	Class: string,
	Type: "Hitscan" | "Projectile",
	Damage: {Head: number, Body: number, Limb: number},
	Range: {Min: number, Max: number},
	Falloff: {Start: number, End: number},
	RateOfFire: number,
	MagazineSize: number,
	ReloadTime: number,
	Spread: {Base: number, Moving: number, Max: number, Recovery: number},
	Recoil: {
		Pattern: {{X: number, Y: number}},
		Multiplier: number,
		Recovery: number
	},
	Movement: {
		WalkSpeed: number,
		AimSpeed: number
	},
	Effects: {
		MuzzleFlash: string?,
		ImpactEffect: string?,
		TracerEffect: string?,
		Sound: string?
	},
	UnlockLevel: number,
	RankedAllowed: boolean
}

-- Match Types
export type MatchConfig = {
	Mode: "Ranked1v1" | "Ranked2v2" | "Casual",
	Map: string,
	RoundsToWin: number,
	RoundTime: number,
	OvertimeEnabled: boolean,
	TeamSize: number,
	AllowedWeapons: {string}?,
	FriendlyFire: boolean,
	Economy: boolean
}

export type MatchResult = {
	MatchId: string,
	Mode: string,
	Map: string,
	Players: {MatchPlayer},
	Teams: {MatchTeam},
	Rounds: {RoundResult},
	Duration: number,
	CompletedAt: number
}

export type MatchPlayer = {
	UserId: UserId,
	Team: number,
	Kills: number,
	Deaths: number,
	Damage: number,
	Headshots: number,
	MMRBefore: number?,
	MMRAfter: number?,
	XPGained: number,
	CreditsGained: number
}

export type MatchTeam = {
	TeamId: number,
	Score: number,
	Players: {UserId}
}

export type RoundResult = {
	RoundNumber: number,
	Winner: number,
	Duration: number,
	PlayerStats: {[UserId]: RoundPlayerStats}
}

export type RoundPlayerStats = {
	Kills: number,
	Deaths: number,
	Damage: number,
	Headshots: number
}

-- Queue Types
export type QueueTicket = {
	TicketId: string,
	UserId: UserId,
	PartyMembers: {UserId}?,
	Mode: string,
	Region: string,
	MMR: number,
	CreatedAt: number,
	Preferences: QueuePreferences?
}

export type QueuePreferences = {
	MaxPing: number?,
	PreferredMaps: {string}?
}

export type MatchmadeGroup = {
	Players: {QueueTicket},
	AverageMMR: number,
	Region: string,
	Mode: string
}

-- Crate Types
export type CrateData = {
	Id: string,
	Name: string,
	Description: string,
	KeyRequired: boolean,
	CreditCost: number?,
	Items: {CrateItem},
	PityCounter: number,
	PityThreshold: number,
	Icon: string
}

export type CrateItem = {
	ItemId: string,
	ItemType: "Skin" | "Banner" | "Pose" | "Effect",
	Rarity: "Common" | "Uncommon" | "Rare" | "Epic" | "Legendary",
	Weight: number,
	ShardValue: number
}

export type CrateResult = {
	ItemId: string,
	ItemType: string,
	Rarity: string,
	IsNew: boolean,
	ShardsAwarded: number?
}

-- Network Types
export type RemoteEventData = {
	[string]: any
}

export type ValidationResult = {
	Success: boolean,
	Error: string?
}

-- Combat Types
export type HitData = {
	Weapon: string,
	Origin: Vector3,
	Direction: Vector3,
	Distance: number,
	HitPart: string?,
	Damage: number,
	Timestamp: number
}

export type FireRequest = {
	Weapon: string,
	Origin: Vector3,
	Direction: Vector3,
	Timestamp: number
}

return {}]])

	-- Constants.lua
	createModuleScript(folders.shared, "Constants", [[--!strict
--[[
	Arena Ascend - Game Constants
	Central configuration for game-wide constants
]]

local Constants = {}

-- Admin Configuration
Constants.ADMIN_USER_IDS = {123456789} -- Replace with actual admin UserIds

-- Game Version
Constants.GAME_VERSION = "1.0.0"
Constants.DATA_VERSION = 1

-- Regions
Constants.REGIONS = {
	"OCE", -- Oceania
	"NAE", -- North America East
	"EU"   -- Europe
}

-- Rank System
Constants.RANKS = {
	{Name = "Tin", Divisions = 3, MinMMR = 0},
	{Name = "Bronze", Divisions = 3, MinMMR = 400},
	{Name = "Silver", Divisions = 3, MinMMR = 800},
	{Name = "Gold", Divisions = 3, MinMMR = 1200},
	{Name = "Platinum", Divisions = 3, MinMMR = 1600},
	{Name = "Diamond", Divisions = 3, MinMMR = 2000},
	{Name = "Onyx", Divisions = 3, MinMMR = 2400},
	{Name = "Ascendant", Divisions = 3, MinMMR = 2800},
	{Name = "Apex", Divisions = 3, MinMMR = 3200},
	{Name = "Top 100", Divisions = 1, MinMMR = 3600}
}

-- Default MMR Values (Glicko-2)
Constants.DEFAULT_MMR = {
	Rating = 1500,
	Deviation = 350,
	Volatility = 0.06
}

-- Match Configuration
Constants.MATCH_CONFIG = {
	Ranked1v1 = {
		RoundsToWin = 7,
		RoundTime = 90,
		OvertimeEnabled = true,
		TeamSize = 1,
		FriendlyFire = false,
		Economy = false
	},
	Ranked2v2 = {
		RoundsToWin = 7,
		RoundTime = 90,
		OvertimeEnabled = true,
		TeamSize = 2,
		FriendlyFire = false,
		Economy = false
	},
	Casual = {
		RoundsToWin = 3,
		RoundTime = 120,
		OvertimeEnabled = false,
		TeamSize = 4,
		FriendlyFire = false,
		Economy = true
	}
}

-- Timing Constants
Constants.TIMING = {
	WARMUP_TIME = 10,
	OVERTIME_TIME = 30,
	POST_ROUND_TIME = 5,
	POST_MATCH_TIME = 15,
	QUEUE_TIMEOUT = 300,
	HEARTBEAT_INTERVAL = 1,
	LAG_COMPENSATION_WINDOW = 0.15
}

-- Economy
Constants.ECONOMY = {
	XP_PER_KILL = 100,
	XP_PER_ROUND_WIN = 200,
	XP_PER_MATCH_WIN = 500,
	CREDITS_PER_KILL = 10,
	CREDITS_PER_ROUND_WIN = 25,
	CREDITS_PER_MATCH_WIN = 100,
	DAILY_CREDIT_BONUS = 500,
	LEVEL_XP_REQUIREMENT = 1000, -- XP needed per level
	SHARD_CONVERSION_RATE = 10 -- Shards per duplicate
}

-- Rate Limiting
Constants.RATE_LIMITS = {
	FIRE_WEAPON = {Requests = 30, Window = 1}, -- 30 shots per second max
	QUEUE_JOIN = {Requests = 5, Window = 10},
	PURCHASE_ITEM = {Requests = 10, Window = 60},
	OPEN_CRATE = {Requests = 20, Window = 60},
	REPORT_PLAYER = {Requests = 5, Window = 300}
}

-- Anti-Exploit
Constants.ANTI_EXPLOIT = {
	MAX_SPEED = 50, -- Maximum allowed character speed
	MAX_JUMP_POWER = 50,
	MAX_FIRE_RATE = 30, -- Shots per second
	MAX_HIT_DISTANCE = 1000,
	MIN_HIT_DISTANCE = 0.1,
	HEARTBEAT_TIMEOUT = 5,
	MAX_PING = 500
}

-- UI Constants
Constants.UI = {
	CROSSHAIR_STYLES = {"Dot", "Cross", "Circle", "Square"},
	DEFAULT_CROSSHAIR_COLOR = Color3.fromRGB(255, 255, 255),
	DEFAULT_SENSITIVITY = 0.5,
	DEFAULT_FOV = 90,
	MIN_FOV = 70,
	MAX_FOV = 110
}

-- Audio
Constants.AUDIO = {
	DEFAULT_MASTER_VOLUME = 0.8,
	DEFAULT_SFX_VOLUME = 0.8,
	DEFAULT_MUSIC_VOLUME = 0.6,
	MAX_AUDIO_DISTANCE = 100
}

-- Colors
Constants.COLORS = {
	TEAM_1 = Color3.fromRGB(0, 162, 255),
	TEAM_2 = Color3.fromRGB(255, 69, 0),
	NEUTRAL = Color3.fromRGB(255, 255, 255),
	SUCCESS = Color3.fromRGB(0, 255, 0),
	WARNING = Color3.fromRGB(255, 255, 0),
	ERROR = Color3.fromRGB(255, 0, 0)
}

return Constants]])

	print("✅ Core modules created!")
end

function ArenaAscendInstaller.CreateRemoteEvents(folders)
	print("🌐 Creating networking layer...")
	
	-- Create RemoteEvents
	createRemoteEvent(folders.events, "QueueJoin")
	createRemoteEvent(folders.events, "QueueLeave")
	createRemoteEvent(folders.events, "EquipLoadout")
	createRemoteEvent(folders.events, "FireWeapon")
	createRemoteEvent(folders.events, "HitConfirm")
	createRemoteEvent(folders.events, "OpenCrate")
	createRemoteEvent(folders.events, "PurchaseItem")
	createRemoteEvent(folders.events, "ReportPlayer")
	createRemoteEvent(folders.events, "Heartbeat")
	
	-- Create RemoteFunctions
	createRemoteFunction(folders.functions, "GetProfile")
	createRemoteFunction(folders.functions, "GetQueues")
	createRemoteFunction(folders.functions, "GetCrateOdds")
	createRemoteFunction(folders.functions, "GetMatchHistory")
	
	print("✅ Networking layer created!")
end

function ArenaAscendInstaller.CreateBasicServices(folders)
	print("⚙️ Creating basic services...")
	
	-- Create a simple ProfileService
	createServerScript(folders.services, "ProfileService", [[--!strict
--[[
	ProfileService - Basic Implementation
	This is a simplified version for initial testing
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local Types = require(ReplicatedStorage.Shared.Types)
local Constants = require(ReplicatedStorage.Shared.Constants)

local ProfileService = {}
local loadedProfiles = {}

-- Create default profile
local function createDefaultProfile(userId: number)
	return {
		UserId = userId,
		Level = 1,
		XP = 0,
		Credits = 1000,
		Keys = 0,
		Shards = 0,
		UnlockedGuns = {Glock = true},
		OwnedSkins = {},
		Loadouts = {
			Primary = {Primary = nil, Secondary = "Glock", Attachments = {}, Skin = nil}
		},
		MMRData = {
			Ranked1v1 = {
				Rating = 1500,
				Deviation = 350,
				Volatility = 0.06,
				GamesPlayed = 0,
				Wins = 0,
				Losses = 0,
				Rank = "Tin",
				Division = 1
			}
		},
		SeasonHistory = {},
		Settings = {
			Sensitivity = 0.5,
			FOV = 90,
			ShowFPS = false,
			ShowPing = true,
			CrosshairStyle = "Cross",
			CrosshairColor = Color3.fromRGB(255, 255, 255),
			MasterVolume = 0.8,
			SFXVolume = 0.8,
			MusicVolume = 0.6,
			Region = "NAE",
			AutoQueue = false
		},
		Statistics = {
			TotalKills = 0,
			TotalDeaths = 0,
			TotalDamage = 0,
			TotalHeadshots = 0,
			TotalRoundsPlayed = 0,
			TotalMatchesPlayed = 0,
			TotalPlaytime = 0,
			WeaponStats = {}
		},
		CreatedAt = os.time(),
		LastPlayed = os.time(),
		Version = 1
	}
end

function ProfileService.GetProfile(userId: number)
	return loadedProfiles[userId]
end

function ProfileService.LoadProfile(userId: number)
	if not loadedProfiles[userId] then
		loadedProfiles[userId] = createDefaultProfile(userId)
		print("✅ Profile loaded for UserId:", userId)
	end
	return loadedProfiles[userId]
end

function ProfileService.Init()
	Players.PlayerAdded:Connect(function(player)
		ProfileService.LoadProfile(player.UserId)
	end)
	
	Players.PlayerRemoving:Connect(function(player)
		loadedProfiles[player.UserId] = nil
	end)
	
	-- Handle existing players
	for _, player in ipairs(Players:GetPlayers()) do
		ProfileService.LoadProfile(player.UserId)
	end
	
	print("✅ ProfileService initialized")
end

return ProfileService]])

	-- Create game initializer
	createServerScript(ServerScriptService, "ArenaAscendInitializer", [[--!strict
--[[
	Arena Ascend - Game Initializer
	Starts all game services
]]

local ServerScriptService = game:GetService("ServerScriptService")

-- Wait for services to load
wait(1)

-- Initialize ProfileService
local ProfileService = require(ServerScriptService.Services.ProfileService)
ProfileService.Init()

print("🎮 Arena Ascend initialized successfully!")
print("📋 Next steps:")
print("   1. Replace ADMIN_USER_IDS in Constants with your UserId")
print("   2. Enable API Services in Game Settings")
print("   3. Add weapon models and UI assets")
print("   4. Create maps with spawn points")
print("   5. Test with Team Test (2+ players)")]])

	print("✅ Basic services created!")
end

-- Main installation function
function ArenaAscendInstaller.Install()
	print("🚀 Installing Arena Ascend...")
	print("⏳ This may take a few seconds...")
	
	local folders = ArenaAscendInstaller.CreateFolderStructure()
	ArenaAscendInstaller.CreateCoreModules(folders)
	ArenaAscendInstaller.CreateRemoteEvents(folders)
	ArenaAscendInstaller.CreateBasicServices(folders)
	
	print("")
	print("🎉 Arena Ascend installation complete!")
	print("🔧 Setup Instructions:")
	print("   1. Go to Game Settings > Security > Enable 'Allow HTTP Requests'")
	print("   2. Go to Game Settings > Security > Enable 'Enable Studio Access to API Services'")
	print("   3. Edit ReplicatedStorage.Shared.Constants and replace ADMIN_USER_IDS with your UserId")
	print("   4. Press F5 to test the basic setup")
	print("   5. Check Output for 'Arena Ascend initialized successfully!'")
	print("")
	print("📚 You can now delete this installer script")
	
	-- Self-destruct after successful installation
	wait(5)
	script:Destroy()
end

-- Auto-run the installer
ArenaAscendInstaller.Install()]])
