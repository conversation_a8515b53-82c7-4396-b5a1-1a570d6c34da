--!strict
--[[
	TableUtil - Table Utility Functions
	Common table manipulation and utility functions
]]

local TableUtil = {}

-- Deep copy a table
function TableUtil.DeepCopy<T>(original: T): T
	if type(original) ~= "table" then
		return original
	end
	
	local copy = {}
	for key, value in pairs(original :: any) do
		copy[key] = TableUtil.DeepCopy(value)
	end
	
	return copy :: T
end

-- Shallow copy a table
function TableUtil.ShallowCopy<T>(original: T): T
	if type(original) ~= "table" then
		return original
	end
	
	local copy = {}
	for key, value in pairs(original :: any) do
		copy[key] = value
	end
	
	return copy :: T
end

-- Merge two tables (shallow)
function TableUtil.Merge<T>(target: T, source: T): T
	for key, value in pairs(source :: any) do
		(target :: any)[key] = value
	end
	
	return target
end

-- Deep merge two tables
function TableUtil.DeepMerge<T>(target: T, source: T): T
	for key, value in pairs(source :: any) do
		if type(value) == "table" and type((target :: any)[key]) == "table" then
			TableUtil.DeepMerge((target :: any)[key], value)
		else
			(target :: any)[key] = value
		end
	end
	
	return target
end

-- Check if table is empty
function TableUtil.IsEmpty(tbl: {[any]: any}): boolean
	return next(tbl) == nil
end

-- Get table size (works for non-array tables)
function TableUtil.Size(tbl: {[any]: any}): number
	local count = 0
	for _ in pairs(tbl) do
		count += 1
	end
	return count
end

-- Get all keys from a table
function TableUtil.Keys<K, V>(tbl: {[K]: V}): {K}
	local keys = {}
	for key in pairs(tbl) do
		table.insert(keys, key)
	end
	return keys
end

-- Get all values from a table
function TableUtil.Values<K, V>(tbl: {[K]: V}): {V}
	local values = {}
	for _, value in pairs(tbl) do
		table.insert(values, value)
	end
	return values
end

-- Find a value in an array and return its index
function TableUtil.Find<T>(array: {T}, value: T): number?
	for i, v in ipairs(array) do
		if v == value then
			return i
		end
	end
	return nil
end

-- Filter an array based on a predicate
function TableUtil.Filter<T>(array: {T}, predicate: (T) -> boolean): {T}
	local result = {}
	for _, value in ipairs(array) do
		if predicate(value) then
			table.insert(result, value)
		end
	end
	return result
end

-- Map an array to a new array
function TableUtil.Map<T, U>(array: {T}, mapper: (T) -> U): {U}
	local result = {}
	for _, value in ipairs(array) do
		table.insert(result, mapper(value))
	end
	return result
end

-- Reduce an array to a single value
function TableUtil.Reduce<T, U>(array: {T}, reducer: (U, T) -> U, initial: U): U
	local accumulator = initial
	for _, value in ipairs(array) do
		accumulator = reducer(accumulator, value)
	end
	return accumulator
end

-- Check if any element in array matches predicate
function TableUtil.Some<T>(array: {T}, predicate: (T) -> boolean): boolean
	for _, value in ipairs(array) do
		if predicate(value) then
			return true
		end
	end
	return false
end

-- Check if all elements in array match predicate
function TableUtil.Every<T>(array: {T}, predicate: (T) -> boolean): boolean
	for _, value in ipairs(array) do
		if not predicate(value) then
			return false
		end
	end
	return true
end

-- Reverse an array
function TableUtil.Reverse<T>(array: {T}): {T}
	local result = {}
	for i = #array, 1, -1 do
		table.insert(result, array[i])
	end
	return result
end

-- Shuffle an array (Fisher-Yates)
function TableUtil.Shuffle<T>(array: {T}, rng: Random?): {T}
	local result = TableUtil.ShallowCopy(array)
	local random = rng or Random.new()
	
	for i = #result, 2, -1 do
		local j = random:NextInteger(1, i)
		result[i], result[j] = result[j], result[i]
	end
	
	return result
end

-- Get a random element from an array
function TableUtil.Sample<T>(array: {T}, rng: Random?): T?
	if #array == 0 then
		return nil
	end
	
	local random = rng or Random.new()
	local index = random:NextInteger(1, #array)
	return array[index]
end

-- Flatten a nested array
function TableUtil.Flatten<T>(array: {{T}}): {T}
	local result = {}
	for _, subArray in ipairs(array) do
		for _, value in ipairs(subArray) do
			table.insert(result, value)
		end
	end
	return result
end

-- Group array elements by a key function
function TableUtil.GroupBy<T, K>(array: {T}, keyFunc: (T) -> K): {[K]: {T}}
	local groups = {}
	for _, value in ipairs(array) do
		local key = keyFunc(value)
		if not groups[key] then
			groups[key] = {}
		end
		table.insert(groups[key], value)
	end
	return groups
end

-- Create a set from an array
function TableUtil.ToSet<T>(array: {T}): {[T]: boolean}
	local set = {}
	for _, value in ipairs(array) do
		set[value] = true
	end
	return set
end

-- Convert set back to array
function TableUtil.FromSet<T>(set: {[T]: boolean}): {T}
	local array = {}
	for key in pairs(set) do
		table.insert(array, key)
	end
	return array
end

-- Check if two tables are equal (deep comparison)
function TableUtil.DeepEqual(a: any, b: any): boolean
	if a == b then
		return true
	end
	
	if type(a) ~= "table" or type(b) ~= "table" then
		return false
	end
	
	for key, value in pairs(a) do
		if not TableUtil.DeepEqual(value, b[key]) then
			return false
		end
	end
	
	for key in pairs(b) do
		if a[key] == nil then
			return false
		end
	end
	
	return true
end

return TableUtil
