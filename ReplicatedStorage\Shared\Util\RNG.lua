--!strict
--[[
	RNG - Seedable Random Number Generator
	Provides deterministic random number generation with additional utilities
]]

local RNG = {}
RNG.__index = RNG

export type RNG = {
	NextNumber: (self: RNG, min: number?, max: number?) -> number,
	NextInteger: (self: RNG, min: number?, max: number?) -> number,
	NextBoolean: (self: RNG, chance: number?) -> boolean,
	NextUnitVector3: (self: RNG) -> Vector3,
	NextVector3: (self: RNG, min: Vector3?, max: Vector3?) -> Vector3,
	NextColor3: (self: RNG) -> Color3,
	Shuffle: <T>(self: RNG, array: {T}) -> {T},
	Sample: <T>(self: RNG, array: {T}) -> T?,
	WeightedSample: <T>(self: RNG, items: {T}, weights: {number}) -> T?,
	SetSeed: (self: RNG, seed: number) -> (),
	GetSeed: (self: RNG) -> number
}

type RNGImpl = {
	_random: Random,
	_seed: number
}

function RNG.new(seed: number?): RNG
	local actualSeed = seed or tick()
	local self: RNGImpl = {
		_random = Random.new(actualSeed),
		_seed = actualSeed
	}
	
	return setmetatable(self, RNG) :: any
end

function RNG:NextNumber(min: number?, max: number?): number
	if min and max then
		return self._random:NextNumber(min, max)
	elseif min then
		return self._random:NextNumber(min, 1)
	else
		return self._random:NextNumber()
	end
end

function RNG:NextInteger(min: number?, max: number?): number
	if min and max then
		return self._random:NextInteger(min, max)
	elseif min then
		return self._random:NextInteger(min, min)
	else
		return self._random:NextInteger(1, 100)
	end
end

function RNG:NextBoolean(chance: number?): boolean
	local probability = chance or 0.5
	return self._random:NextNumber() < probability
end

function RNG:NextUnitVector3(): Vector3
	-- Generate a random point on the unit sphere
	local theta = self._random:NextNumber(0, 2 * math.pi)
	local phi = math.acos(self._random:NextNumber(-1, 1))
	
	local x = math.sin(phi) * math.cos(theta)
	local y = math.sin(phi) * math.sin(theta)
	local z = math.cos(phi)
	
	return Vector3.new(x, y, z)
end

function RNG:NextVector3(min: Vector3?, max: Vector3?): Vector3
	local minVec = min or Vector3.new(0, 0, 0)
	local maxVec = max or Vector3.new(1, 1, 1)
	
	return Vector3.new(
		self._random:NextNumber(minVec.X, maxVec.X),
		self._random:NextNumber(minVec.Y, maxVec.Y),
		self._random:NextNumber(minVec.Z, maxVec.Z)
	)
end

function RNG:NextColor3(): Color3
	return Color3.new(
		self._random:NextNumber(),
		self._random:NextNumber(),
		self._random:NextNumber()
	)
end

function RNG:Shuffle<T>(array: {T}): {T}
	local result = table.clone(array)
	
	for i = #result, 2, -1 do
		local j = self._random:NextInteger(1, i)
		result[i], result[j] = result[j], result[i]
	end
	
	return result
end

function RNG:Sample<T>(array: {T}): T?
	if #array == 0 then
		return nil
	end
	
	local index = self._random:NextInteger(1, #array)
	return array[index]
end

function RNG:WeightedSample<T>(items: {T}, weights: {number}): T?
	if #items == 0 or #weights == 0 or #items ~= #weights then
		return nil
	end
	
	-- Calculate total weight
	local totalWeight = 0
	for _, weight in ipairs(weights) do
		totalWeight += weight
	end
	
	if totalWeight <= 0 then
		return nil
	end
	
	-- Generate random number and find corresponding item
	local randomValue = self._random:NextNumber(0, totalWeight)
	local currentWeight = 0
	
	for i, weight in ipairs(weights) do
		currentWeight += weight
		if randomValue <= currentWeight then
			return items[i]
		end
	end
	
	-- Fallback (should never reach here)
	return items[#items]
end

function RNG:SetSeed(seed: number)
	self._seed = seed
	self._random = Random.new(seed)
end

function RNG:GetSeed(): number
	return self._seed
end

-- Static utility functions
function RNG.FromString(str: string): RNG
	-- Create a simple hash from string
	local hash = 0
	for i = 1, #str do
		hash = (hash * 31 + string.byte(str, i)) % 2147483647
	end
	return RNG.new(hash)
end

function RNG.FromTime(): RNG
	return RNG.new(os.time())
end

function RNG.FromTick(): RNG
	return RNG.new(tick())
end

-- Gaussian/Normal distribution using Box-Muller transform
function RNG:NextGaussian(mean: number?, stdDev: number?): number
	local mu = mean or 0
	local sigma = stdDev or 1
	
	-- Box-Muller transform
	local u1 = self._random:NextNumber()
	local u2 = self._random:NextNumber()
	
	local z0 = math.sqrt(-2 * math.log(u1)) * math.cos(2 * math.pi * u2)
	return z0 * sigma + mu
end

-- Exponential distribution
function RNG:NextExponential(lambda: number?): number
	local rate = lambda or 1
	local u = self._random:NextNumber()
	return -math.log(1 - u) / rate
end

-- Poisson distribution (approximation for small lambda)
function RNG:NextPoisson(lambda: number): number
	local L = math.exp(-lambda)
	local k = 0
	local p = 1
	
	repeat
		k += 1
		p *= self._random:NextNumber()
	until p <= L
	
	return k - 1
end

return RNG
