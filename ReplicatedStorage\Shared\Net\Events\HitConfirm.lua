--!strict
--[[
	HitConfirm RemoteEvent
	Server-to-client hit confirmation for feedback
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local HitConfirm = Instance.new("RemoteEvent")
HitConfirm.Name = "HitConfirm"
HitConfirm.Parent = script.Parent

-- Type definitions for the event data
export type HitConfirmData = {
	HitType: "Hit" | "Headshot" | "Kill" | "Assist",
	Damage: number,
	Target: string, -- Target player name
	WeaponId: string,
	Distance: number,
	HitPart: "Head" | "Body" | "Limb"
}

return HitConfirm
