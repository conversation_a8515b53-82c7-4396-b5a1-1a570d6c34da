--!strict
--[[
	WeaponData - Weapon Statistics and Configuration
	Defines all weapons with balanced stats for competitive play
]]

local WeaponData = {}

-- Weapon Categories
WeaponData.CATEGORIES = {
	Pistol = {Name = "Pistols", Icon = "rbxassetid://0"},
	SMG = {Name = "Submachine Guns", Icon = "rbxassetid://0"},
	Rifle = {Name = "Assault Rifles", Icon = "rbxassetid://0"},
	DMR = {Name = "Marksman Rifles", Icon = "rbxassetid://0"},
	Sniper = {Name = "Sniper Rifles", Icon = "rbxassetid://0"},
	Shotgun = {Name = "Shotguns", Icon = "rbxassetid://0"},
	LMG = {Name = "Light Machine Guns", Icon = "rbxassetid://0"}
}

-- Weapon Definitions
WeaponData.WEAPONS = {
	-- PISTOLS
	Glock = {
		Name = "Glock-18",
		Class = "Pistol",
		Type = "Hitscan",
		Damage = {Head = 70, Body = 35, Limb = 28},
		Range = {Min = 0, Max = 50},
		Falloff = {Start = 15, End = 35},
		RateOfFire = 400, -- RPM
		MagazineSize = 20,
		ReloadTime = 2.2,
		Spread = {Base = 0.02, Moving = 0.04, Max = 0.15, Recovery = 0.1},
		Recoil = {
			Pattern = {{X = 0, Y = 0.3}, {X = -0.1, Y = 0.4}, {X = 0.1, Y = 0.3}, {X = 0, Y = 0.4}},
			Multiplier = 1.0,
			Recovery = 0.15
		},
		Movement = {WalkSpeed = 16, AimSpeed = 12},
		Effects = {
			MuzzleFlash = "PistolMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "PistolTracer",
			Sound = "GlockFire"
		},
		UnlockLevel = 1,
		RankedAllowed = true
	},
	
	USP = {
		Name = "USP-S",
		Class = "Pistol",
		Type = "Hitscan",
		Damage = {Head = 85, Body = 42, Limb = 34},
		Range = {Min = 0, Max = 60},
		Falloff = {Start = 20, End = 45},
		RateOfFire = 320,
		MagazineSize = 12,
		ReloadTime = 2.5,
		Spread = {Base = 0.015, Moving = 0.035, Max = 0.12, Recovery = 0.12},
		Recoil = {
			Pattern = {{X = 0, Y = 0.4}, {X = -0.15, Y = 0.5}, {X = 0.15, Y = 0.4}},
			Multiplier = 1.2,
			Recovery = 0.18
		},
		Movement = {WalkSpeed = 16, AimSpeed = 11},
		Effects = {
			MuzzleFlash = "SuppressedMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "SuppressedTracer",
			Sound = "USPFire"
		},
		UnlockLevel = 8,
		RankedAllowed = true
	},
	
	Deagle = {
		Name = "Desert Eagle",
		Class = "Pistol",
		Type = "Hitscan",
		Damage = {Head = 140, Body = 70, Limb = 56},
		Range = {Min = 0, Max = 80},
		Falloff = {Start = 30, End = 60},
		RateOfFire = 180,
		MagazineSize = 7,
		ReloadTime = 3.0,
		Spread = {Base = 0.025, Moving = 0.06, Max = 0.25, Recovery = 0.08},
		Recoil = {
			Pattern = {{X = 0, Y = 0.8}, {X = -0.3, Y = 0.9}, {X = 0.3, Y = 0.8}, {X = 0, Y = 1.0}},
			Multiplier = 2.0,
			Recovery = 0.12
		},
		Movement = {WalkSpeed = 15, AimSpeed = 9},
		Effects = {
			MuzzleFlash = "HeavyMuzzle",
			ImpactEffect = "HeavyImpact",
			TracerEffect = "HeavyTracer",
			Sound = "DeagleFire"
		},
		UnlockLevel = 25,
		RankedAllowed = true
	},
	
	-- SUBMACHINE GUNS
	MP5 = {
		Name = "MP5",
		Class = "SMG",
		Type = "Hitscan",
		Damage = {Head = 60, Body = 30, Limb = 24},
		Range = {Min = 0, Max = 40},
		Falloff = {Start = 10, End = 30},
		RateOfFire = 800,
		MagazineSize = 30,
		ReloadTime = 2.8,
		Spread = {Base = 0.03, Moving = 0.04, Max = 0.18, Recovery = 0.15},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.2}, {X = -0.1, Y = 0.25}, {X = 0.1, Y = 0.2}, {X = -0.05, Y = 0.3},
				{X = 0.05, Y = 0.25}, {X = -0.15, Y = 0.35}, {X = 0.15, Y = 0.3}
			},
			Multiplier = 0.8,
			Recovery = 0.2
		},
		Movement = {WalkSpeed = 18, AimSpeed = 14},
		Effects = {
			MuzzleFlash = "SMGMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "SMGTracer",
			Sound = "MP5Fire"
		},
		UnlockLevel = 5,
		RankedAllowed = true
	},
	
	UMP = {
		Name = "UMP-45",
		Class = "SMG",
		Type = "Hitscan",
		Damage = {Head = 75, Body = 38, Limb = 30},
		Range = {Min = 0, Max = 45},
		Falloff = {Start = 15, End = 35},
		RateOfFire = 600,
		MagazineSize = 25,
		ReloadTime = 3.2,
		Spread = {Base = 0.025, Moving = 0.045, Max = 0.16, Recovery = 0.12},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.3}, {X = -0.15, Y = 0.4}, {X = 0.15, Y = 0.35}, {X = -0.1, Y = 0.45},
				{X = 0.1, Y = 0.4}, {X = -0.2, Y = 0.5}
			},
			Multiplier = 1.0,
			Recovery = 0.18
		},
		Movement = {WalkSpeed = 17, AimSpeed = 13},
		Effects = {
			MuzzleFlash = "SMGMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "SMGTracer",
			Sound = "UMPFire"
		},
		UnlockLevel = 12,
		RankedAllowed = true
	},
	
	P90 = {
		Name = "P90",
		Class = "SMG",
		Type = "Hitscan",
		Damage = {Head = 55, Body = 28, Limb = 22},
		Range = {Min = 0, Max = 35},
		Falloff = {Start = 8, End = 25},
		RateOfFire = 900,
		MagazineSize = 50,
		ReloadTime = 3.5,
		Spread = {Base = 0.035, Moving = 0.05, Max = 0.2, Recovery = 0.18},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.15}, {X = -0.08, Y = 0.2}, {X = 0.08, Y = 0.18}, {X = -0.05, Y = 0.25},
				{X = 0.05, Y = 0.22}, {X = -0.12, Y = 0.3}, {X = 0.12, Y = 0.28}, {X = 0, Y = 0.35}
			},
			Multiplier = 0.6,
			Recovery = 0.25
		},
		Movement = {WalkSpeed = 19, AimSpeed = 15},
		Effects = {
			MuzzleFlash = "SMGMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "SMGTracer",
			Sound = "P90Fire"
		},
		UnlockLevel = 20,
		RankedAllowed = true
	},
	
	-- ASSAULT RIFLES
	M4A1 = {
		Name = "M4A1-S",
		Class = "Rifle",
		Type = "Hitscan",
		Damage = {Head = 92, Body = 46, Limb = 37},
		Range = {Min = 0, Max = 100},
		Falloff = {Start = 40, End = 80},
		RateOfFire = 666,
		MagazineSize = 20,
		ReloadTime = 3.1,
		Spread = {Base = 0.02, Moving = 0.04, Max = 0.14, Recovery = 0.12},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.4}, {X = -0.1, Y = 0.5}, {X = 0.1, Y = 0.45}, {X = -0.15, Y = 0.6},
				{X = 0.15, Y = 0.55}, {X = -0.2, Y = 0.7}, {X = 0.2, Y = 0.65}, {X = -0.1, Y = 0.8},
				{X = 0.1, Y = 0.75}, {X = 0, Y = 0.9}
			},
			Multiplier = 1.0,
			Recovery = 0.15
		},
		Movement = {WalkSpeed = 14, AimSpeed = 10},
		Effects = {
			MuzzleFlash = "SuppressedMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "RifleTracer",
			Sound = "M4A1Fire"
		},
		UnlockLevel = 10,
		RankedAllowed = true
	},
	
	M4A4 = {
		Name = "M4A4",
		Class = "Rifle",
		Type = "Hitscan",
		Damage = {Head = 88, Body = 44, Limb = 35},
		Range = {Min = 0, Max = 100},
		Falloff = {Start = 40, End = 80},
		RateOfFire = 750,
		MagazineSize = 30,
		ReloadTime = 3.3,
		Spread = {Base = 0.022, Moving = 0.042, Max = 0.16, Recovery = 0.1},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.35}, {X = -0.12, Y = 0.45}, {X = 0.12, Y = 0.4}, {X = -0.18, Y = 0.55},
				{X = 0.18, Y = 0.5}, {X = -0.25, Y = 0.65}, {X = 0.25, Y = 0.6}, {X = -0.15, Y = 0.75},
				{X = 0.15, Y = 0.7}, {X = -0.08, Y = 0.85}, {X = 0.08, Y = 0.8}
			},
			Multiplier = 1.1,
			Recovery = 0.12
		},
		Movement = {WalkSpeed = 14, AimSpeed = 10},
		Effects = {
			MuzzleFlash = "RifleMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "RifleTracer",
			Sound = "M4A4Fire"
		},
		UnlockLevel = 15,
		RankedAllowed = true
	},
	
	AK47 = {
		Name = "AK-47",
		Class = "Rifle",
		Type = "Hitscan",
		Damage = {Head = 110, Body = 55, Limb = 44},
		Range = {Min = 0, Max = 120},
		Falloff = {Start = 50, End = 100},
		RateOfFire = 600,
		MagazineSize = 30,
		ReloadTime = 2.8,
		Spread = {Base = 0.025, Moving = 0.05, Max = 0.18, Recovery = 0.08},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.5}, {X = -0.2, Y = 0.7}, {X = 0.3, Y = 0.6}, {X = -0.4, Y = 0.8},
				{X = 0.5, Y = 0.7}, {X = -0.3, Y = 0.9}, {X = 0.2, Y = 0.8}, {X = -0.1, Y = 1.0},
				{X = 0.1, Y = 0.9}, {X = -0.15, Y = 1.1}, {X = 0.15, Y = 1.0}
			},
			Multiplier = 1.5,
			Recovery = 0.1
		},
		Movement = {WalkSpeed = 13, AimSpeed = 9},
		Effects = {
			MuzzleFlash = "RifleMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "RifleTracer",
			Sound = "AK47Fire"
		},
		UnlockLevel = 20,
		RankedAllowed = true
	}
}


	-- MARKSMAN RIFLES
	SCAR20 = {
		Name = "SCAR-20",
		Class = "DMR",
		Type = "Hitscan",
		Damage = {Head = 120, Body = 60, Limb = 48},
		Range = {Min = 0, Max = 150},
		Falloff = {Start = 60, End = 120},
		RateOfFire = 240,
		MagazineSize = 20,
		ReloadTime = 3.8,
		Spread = {Base = 0.015, Moving = 0.06, Max = 0.12, Recovery = 0.08},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.6}, {X = -0.2, Y = 0.8}, {X = 0.2, Y = 0.7}, {X = -0.15, Y = 0.9},
				{X = 0.15, Y = 0.8}, {X = -0.1, Y = 1.0}
			},
			Multiplier = 1.3,
			Recovery = 0.1
		},
		Movement = {WalkSpeed = 12, AimSpeed = 8},
		Effects = {
			MuzzleFlash = "DMRMuzzle",
			ImpactEffect = "HeavyImpact",
			TracerEffect = "DMRTracer",
			Sound = "SCAR20Fire"
		},
		UnlockLevel = 30,
		RankedAllowed = true
	},

	G3SG1 = {
		Name = "G3SG1",
		Class = "DMR",
		Type = "Hitscan",
		Damage = {Head = 115, Body = 58, Limb = 46},
		Range = {Min = 0, Max = 140},
		Falloff = {Start = 55, End = 110},
		RateOfFire = 260,
		MagazineSize = 20,
		ReloadTime = 3.5,
		Spread = {Base = 0.018, Moving = 0.055, Max = 0.14, Recovery = 0.09},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.55}, {X = -0.18, Y = 0.75}, {X = 0.18, Y = 0.65}, {X = -0.12, Y = 0.85},
				{X = 0.12, Y = 0.75}, {X = -0.08, Y = 0.95}
			},
			Multiplier = 1.2,
			Recovery = 0.12
		},
		Movement = {WalkSpeed = 12, AimSpeed = 8},
		Effects = {
			MuzzleFlash = "DMRMuzzle",
			ImpactEffect = "HeavyImpact",
			TracerEffect = "DMRTracer",
			Sound = "G3SG1Fire"
		},
		UnlockLevel = 35,
		RankedAllowed = true
	},

	-- SNIPER RIFLES
	AWP = {
		Name = "AWP",
		Class = "Sniper",
		Type = "Hitscan",
		Damage = {Head = 300, Body = 150, Limb = 120},
		Range = {Min = 0, Max = 300},
		Falloff = {Start = 100, End = 250},
		RateOfFire = 40,
		MagazineSize = 10,
		ReloadTime = 4.2,
		Spread = {Base = 0.005, Moving = 0.08, Max = 0.1, Recovery = 0.05},
		Recoil = {
			Pattern = {{X = 0, Y = 1.5}, {X = -0.3, Y = 2.0}, {X = 0.3, Y = 1.8}},
			Multiplier = 3.0,
			Recovery = 0.05
		},
		Movement = {WalkSpeed = 10, AimSpeed = 6},
		Effects = {
			MuzzleFlash = "SniperMuzzle",
			ImpactEffect = "SniperImpact",
			TracerEffect = "SniperTracer",
			Sound = "AWPFire"
		},
		UnlockLevel = 35,
		RankedAllowed = true
	},

	Scout = {
		Name = "SSG 08",
		Class = "Sniper",
		Type = "Hitscan",
		Damage = {Head = 200, Body = 100, Limb = 80},
		Range = {Min = 0, Max = 250},
		Falloff = {Start = 80, End = 200},
		RateOfFire = 60,
		MagazineSize = 10,
		ReloadTime = 3.8,
		Spread = {Base = 0.008, Moving = 0.06, Max = 0.08, Recovery = 0.08},
		Recoil = {
			Pattern = {{X = 0, Y = 1.2}, {X = -0.2, Y = 1.6}, {X = 0.2, Y = 1.4}},
			Multiplier = 2.5,
			Recovery = 0.08
		},
		Movement = {WalkSpeed = 12, AimSpeed = 8},
		Effects = {
			MuzzleFlash = "SniperMuzzle",
			ImpactEffect = "SniperImpact",
			TracerEffect = "SniperTracer",
			Sound = "ScoutFire"
		},
		UnlockLevel = 28,
		RankedAllowed = true
	},

	-- SHOTGUNS
	Nova = {
		Name = "Nova",
		Class = "Shotgun",
		Type = "Hitscan",
		Damage = {Head = 200, Body = 100, Limb = 80}, -- Per pellet, 8 pellets
		Range = {Min = 0, Max = 25},
		Falloff = {Start = 5, End = 20},
		RateOfFire = 68,
		MagazineSize = 8,
		ReloadTime = 4.5,
		Spread = {Base = 0.15, Moving = 0.2, Max = 0.25, Recovery = 0.1},
		Recoil = {
			Pattern = {{X = 0, Y = 1.0}, {X = -0.4, Y = 1.5}, {X = 0.4, Y = 1.3}},
			Multiplier = 2.0,
			Recovery = 0.15
		},
		Movement = {WalkSpeed = 15, AimSpeed = 11},
		Effects = {
			MuzzleFlash = "ShotgunMuzzle",
			ImpactEffect = "ShotgunImpact",
			TracerEffect = "ShotgunSpread",
			Sound = "NovaFire"
		},
		UnlockLevel = 18,
		RankedAllowed = false, -- Shotguns disabled in ranked
		PelletCount = 8
	},

	XM1014 = {
		Name = "XM1014",
		Class = "Shotgun",
		Type = "Hitscan",
		Damage = {Head = 180, Body = 90, Limb = 72}, -- Per pellet, 6 pellets
		Range = {Min = 0, Max = 30},
		Falloff = {Start = 8, End = 25},
		RateOfFire = 171,
		MagazineSize = 7,
		ReloadTime = 3.8,
		Spread = {Base = 0.12, Moving = 0.18, Max = 0.22, Recovery = 0.12},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.8}, {X = -0.3, Y = 1.2}, {X = 0.3, Y = 1.0}, {X = -0.2, Y = 1.4},
				{X = 0.2, Y = 1.2}
			},
			Multiplier = 1.8,
			Recovery = 0.18
		},
		Movement = {WalkSpeed = 16, AimSpeed = 12},
		Effects = {
			MuzzleFlash = "ShotgunMuzzle",
			ImpactEffect = "ShotgunImpact",
			TracerEffect = "ShotgunSpread",
			Sound = "XM1014Fire"
		},
		UnlockLevel = 25,
		RankedAllowed = false,
		PelletCount = 6
	},

	-- LIGHT MACHINE GUNS
	M249 = {
		Name = "M249",
		Class = "LMG",
		Type = "Hitscan",
		Damage = {Head = 95, Body = 48, Limb = 38},
		Range = {Min = 0, Max = 120},
		Falloff = {Start = 45, End = 90},
		RateOfFire = 750,
		MagazineSize = 100,
		ReloadTime = 5.5,
		Spread = {Base = 0.04, Moving = 0.08, Max = 0.25, Recovery = 0.05},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.3}, {X = -0.15, Y = 0.4}, {X = 0.15, Y = 0.35}, {X = -0.25, Y = 0.5},
				{X = 0.25, Y = 0.45}, {X = -0.35, Y = 0.6}, {X = 0.35, Y = 0.55}, {X = -0.2, Y = 0.7},
				{X = 0.2, Y = 0.65}, {X = -0.1, Y = 0.8}, {X = 0.1, Y = 0.75}, {X = 0, Y = 0.9}
			},
			Multiplier = 0.9,
			Recovery = 0.08
		},
		Movement = {WalkSpeed = 11, AimSpeed = 7},
		Effects = {
			MuzzleFlash = "LMGMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "LMGTracer",
			Sound = "M249Fire"
		},
		UnlockLevel = 40,
		RankedAllowed = false -- LMGs disabled in ranked
	},

	Negev = {
		Name = "Negev",
		Class = "LMG",
		Type = "Hitscan",
		Damage = {Head = 90, Body = 45, Limb = 36},
		Range = {Min = 0, Max = 110},
		Falloff = {Start = 40, End = 85},
		RateOfFire = 1000,
		MagazineSize = 150,
		ReloadTime = 6.0,
		Spread = {Base = 0.05, Moving = 0.1, Max = 0.3, Recovery = 0.03},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.4}, {X = -0.2, Y = 0.5}, {X = 0.2, Y = 0.45}, {X = -0.3, Y = 0.6},
				{X = 0.3, Y = 0.55}, {X = -0.4, Y = 0.7}, {X = 0.4, Y = 0.65}, {X = -0.25, Y = 0.8},
				{X = 0.25, Y = 0.75}, {X = -0.15, Y = 0.9}, {X = 0.15, Y = 0.85}
			},
			Multiplier = 0.8,
			Recovery = 0.06
		},
		Movement = {WalkSpeed = 10, AimSpeed = 6},
		Effects = {
			MuzzleFlash = "LMGMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "LMGTracer",
			Sound = "NegevFire"
		},
		UnlockLevel = 45,
		RankedAllowed = false
	},

	-- ADDITIONAL WEAPONS FOR VARIETY
	Famas = {
		Name = "FAMAS",
		Class = "Rifle",
		Type = "Hitscan",
		Damage = {Head = 85, Body = 43, Limb = 34},
		Range = {Min = 0, Max = 90},
		Falloff = {Start = 35, End = 70},
		RateOfFire = 666,
		MagazineSize = 25,
		ReloadTime = 3.3,
		Spread = {Base = 0.028, Moving = 0.048, Max = 0.17, Recovery = 0.11},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.4}, {X = -0.15, Y = 0.55}, {X = 0.15, Y = 0.5}, {X = -0.2, Y = 0.65},
				{X = 0.2, Y = 0.6}, {X = -0.1, Y = 0.75}, {X = 0.1, Y = 0.7}
			},
			Multiplier = 1.1,
			Recovery = 0.13
		},
		Movement = {WalkSpeed = 14, AimSpeed = 10},
		Effects = {
			MuzzleFlash = "RifleMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "RifleTracer",
			Sound = "FamasFire"
		},
		UnlockLevel = 22,
		RankedAllowed = true
	},

	Galil = {
		Name = "Galil AR",
		Class = "Rifle",
		Type = "Hitscan",
		Damage = {Head = 95, Body = 48, Limb = 38},
		Range = {Min = 0, Max = 95},
		Falloff = {Start = 38, End = 75},
		RateOfFire = 666,
		MagazineSize = 35,
		ReloadTime = 2.9,
		Spread = {Base = 0.026, Moving = 0.046, Max = 0.16, Recovery = 0.12},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.45}, {X = -0.18, Y = 0.6}, {X = 0.18, Y = 0.55}, {X = -0.25, Y = 0.7},
				{X = 0.25, Y = 0.65}, {X = -0.15, Y = 0.8}, {X = 0.15, Y = 0.75}
			},
			Multiplier = 1.2,
			Recovery = 0.11
		},
		Movement = {WalkSpeed = 14, AimSpeed = 10},
		Effects = {
			MuzzleFlash = "RifleMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "RifleTracer",
			Sound = "GalilFire"
		},
		UnlockLevel = 26,
		RankedAllowed = true
	},

	MAC10 = {
		Name = "MAC-10",
		Class = "SMG",
		Type = "Hitscan",
		Damage = {Head = 65, Body = 33, Limb = 26},
		Range = {Min = 0, Max = 35},
		Falloff = {Start = 8, End = 25},
		RateOfFire = 857,
		MagazineSize = 30,
		ReloadTime = 2.6,
		Spread = {Base = 0.04, Moving = 0.055, Max = 0.22, Recovery = 0.16},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.25}, {X = -0.12, Y = 0.35}, {X = 0.12, Y = 0.3}, {X = -0.08, Y = 0.4},
				{X = 0.08, Y = 0.35}, {X = -0.18, Y = 0.45}
			},
			Multiplier = 0.9,
			Recovery = 0.22
		},
		Movement = {WalkSpeed = 19, AimSpeed = 15},
		Effects = {
			MuzzleFlash = "SMGMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "SMGTracer",
			Sound = "MAC10Fire"
		},
		UnlockLevel = 16,
		RankedAllowed = true
	},

	MP7 = {
		Name = "MP7",
		Class = "SMG",
		Type = "Hitscan",
		Damage = {Head = 58, Body = 29, Limb = 23},
		Range = {Min = 0, Max = 42},
		Falloff = {Start = 12, End = 32},
		RateOfFire = 750,
		MagazineSize = 30,
		ReloadTime = 3.1,
		Spread = {Base = 0.032, Moving = 0.042, Max = 0.19, Recovery = 0.17},
		Recoil = {
			Pattern = {
				{X = 0, Y = 0.22}, {X = -0.1, Y = 0.28}, {X = 0.1, Y = 0.25}, {X = -0.06, Y = 0.32},
				{X = 0.06, Y = 0.28}, {X = -0.14, Y = 0.38}, {X = 0.14, Y = 0.35}
			},
			Multiplier = 0.75,
			Recovery = 0.19
		},
		Movement = {WalkSpeed = 18, AimSpeed = 14},
		Effects = {
			MuzzleFlash = "SMGMuzzle",
			ImpactEffect = "BulletImpact",
			TracerEffect = "SMGTracer",
			Sound = "MP7Fire"
		},
		UnlockLevel = 32,
		RankedAllowed = true
	}
}

-- Ranked Weapon Sets (standardized stats for competitive play)
WeaponData.RANKED_SETS = {
	Standard = {
		Primary = {"M4A1", "M4A4", "AK47", "Famas", "Galil"},
		Secondary = {"Glock", "USP", "Deagle"},
		Sniper = {"AWP", "Scout"},
		DMR = {"SCAR20", "G3SG1"}
	},

	Pistol_Round = {
		Primary = {},
		Secondary = {"Glock", "USP"},
		Sniper = {},
		DMR = {}
	}
}

-- Helper Functions
function WeaponData.GetWeapon(weaponId: string): any?
	return WeaponData.WEAPONS[weaponId]
end

function WeaponData.GetWeaponsByClass(class: string): {any}
	local weapons = {}
	for _, weapon in pairs(WeaponData.WEAPONS) do
		if weapon.Class == class then
			table.insert(weapons, weapon)
		end
	end
	return weapons
end

function WeaponData.GetRankedWeapons(): {any}
	local weapons = {}
	for _, weapon in pairs(WeaponData.WEAPONS) do
		if weapon.RankedAllowed then
			table.insert(weapons, weapon)
		end
	end
	return weapons
end

function WeaponData.GetUnlockedWeapons(playerLevel: number): {any}
	local weapons = {}
	for _, weapon in pairs(WeaponData.WEAPONS) do
		if weapon.UnlockLevel <= playerLevel then
			table.insert(weapons, weapon)
		end
	end
	return weapons
end

function WeaponData.CalculateDamage(weaponId: string, hitPart: string, distance: number): number
	local weapon = WeaponData.GetWeapon(weaponId)
	if not weapon then
		return 0
	end

	-- Get base damage for hit part
	local baseDamage = weapon.Damage.Body
	if hitPart == "Head" then
		baseDamage = weapon.Damage.Head
	elseif hitPart == "Limb" then
		baseDamage = weapon.Damage.Limb
	end

	-- Apply distance falloff
	local falloffStart = weapon.Falloff.Start
	local falloffEnd = weapon.Falloff.End

	if distance <= falloffStart then
		return baseDamage
	elseif distance >= falloffEnd then
		return math.floor(baseDamage * 0.5) -- 50% damage at max range
	else
		local falloffProgress = (distance - falloffStart) / (falloffEnd - falloffStart)
		local damageMultiplier = 1 - (falloffProgress * 0.5)
		return math.floor(baseDamage * damageMultiplier)
	end
end

function WeaponData.GetRecoilPattern(weaponId: string, shotNumber: number): {X: number, Y: number}?
	local weapon = WeaponData.GetWeapon(weaponId)
	if not weapon or not weapon.Recoil.Pattern then
		return nil
	end

	local pattern = weapon.Recoil.Pattern
	local index = math.min(shotNumber, #pattern)
	return pattern[index]
end

function WeaponData.IsValidLoadout(primary: string?, secondary: string?, mode: string): (boolean, string?)
	-- Check if weapons exist
	if primary and not WeaponData.GetWeapon(primary) then
		return false, "Invalid primary weapon"
	end

	if secondary and not WeaponData.GetWeapon(secondary) then
		return false, "Invalid secondary weapon"
	end

	-- Check ranked restrictions
	if mode == "Ranked1v1" or mode == "Ranked2v2" then
		if primary then
			local primaryWeapon = WeaponData.GetWeapon(primary)
			if not primaryWeapon.RankedAllowed then
				return false, "Primary weapon not allowed in ranked"
			end
		end

		if secondary then
			local secondaryWeapon = WeaponData.GetWeapon(secondary)
			if not secondaryWeapon.RankedAllowed then
				return false, "Secondary weapon not allowed in ranked"
			end
		end
	end

	return true, nil
end

return WeaponData
