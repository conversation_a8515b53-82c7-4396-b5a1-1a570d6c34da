--!strict
--[[
	Arena Ascend - Game Constants
	Central configuration for game-wide constants
]]

local Constants = {}

-- Admin Configuration
Constants.ADMIN_USER_IDS = {123456789} -- Replace with actual admin UserIds

-- Game Version
Constants.GAME_VERSION = "1.0.0"
Constants.DATA_VERSION = 1

-- Regions
Constants.REGIONS = {
	"OCE", -- Oceania
	"NAE", -- North America East
	"EU"   -- Europe
}

-- Rank System
Constants.RANKS = {
	{Name = "Tin", Divisions = 3, MinMMR = 0},
	{Name = "Bronze", Divisions = 3, MinMMR = 400},
	{Name = "Silver", Divisions = 3, MinMMR = 800},
	{Name = "Gold", Divisions = 3, MinMMR = 1200},
	{Name = "Platinum", Divisions = 3, MinMMR = 1600},
	{Name = "Diamond", Divisions = 3, MinMMR = 2000},
	{Name = "Onyx", Divisions = 3, MinMMR = 2400},
	{Name = "Ascendant", Divisions = 3, MinMMR = 2800},
	{Name = "Apex", Divisions = 3, MinMMR = 3200},
	{Name = "Top 100", Divisions = 1, MinMMR = 3600}
}

-- Default MMR Values (Glicko-2)
Constants.DEFAULT_MMR = {
	Rating = 1500,
	Deviation = 350,
	Volatility = 0.06
}

-- Match Configuration
Constants.MATCH_CONFIG = {
	Ranked1v1 = {
		RoundsToWin = 7,
		RoundTime = 90,
		OvertimeEnabled = true,
		TeamSize = 1,
		FriendlyFire = false,
		Economy = false
	},
	Ranked2v2 = {
		RoundsToWin = 7,
		RoundTime = 90,
		OvertimeEnabled = true,
		TeamSize = 2,
		FriendlyFire = false,
		Economy = false
	},
	Casual = {
		RoundsToWin = 3,
		RoundTime = 120,
		OvertimeEnabled = false,
		TeamSize = 4,
		FriendlyFire = false,
		Economy = true
	}
}

-- Timing Constants
Constants.TIMING = {
	WARMUP_TIME = 10,
	OVERTIME_TIME = 30,
	POST_ROUND_TIME = 5,
	POST_MATCH_TIME = 15,
	QUEUE_TIMEOUT = 300,
	HEARTBEAT_INTERVAL = 1,
	LAG_COMPENSATION_WINDOW = 0.15
}

-- Economy
Constants.ECONOMY = {
	XP_PER_KILL = 100,
	XP_PER_ROUND_WIN = 200,
	XP_PER_MATCH_WIN = 500,
	CREDITS_PER_KILL = 10,
	CREDITS_PER_ROUND_WIN = 25,
	CREDITS_PER_MATCH_WIN = 100,
	DAILY_CREDIT_BONUS = 500,
	LEVEL_XP_REQUIREMENT = 1000, -- XP needed per level
	SHARD_CONVERSION_RATE = 10 -- Shards per duplicate
}

-- Weapon Categories
Constants.WEAPON_CLASSES = {
	"Pistol",
	"SMG", 
	"Rifle",
	"DMR",
	"Sniper",
	"Shotgun",
	"LMG"
}

-- Damage Multipliers
Constants.DAMAGE_MULTIPLIERS = {
	Head = 2.0,
	Body = 1.0,
	Limb = 0.8
}

-- Rate Limiting
Constants.RATE_LIMITS = {
	FIRE_WEAPON = {Requests = 30, Window = 1}, -- 30 shots per second max
	QUEUE_JOIN = {Requests = 5, Window = 10},
	PURCHASE_ITEM = {Requests = 10, Window = 60},
	OPEN_CRATE = {Requests = 20, Window = 60},
	REPORT_PLAYER = {Requests = 5, Window = 300}
}

-- Anti-Exploit
Constants.ANTI_EXPLOIT = {
	MAX_SPEED = 50, -- Maximum allowed character speed
	MAX_JUMP_POWER = 50,
	MAX_FIRE_RATE = 30, -- Shots per second
	MAX_HIT_DISTANCE = 1000,
	MIN_HIT_DISTANCE = 0.1,
	HEARTBEAT_TIMEOUT = 5,
	MAX_PING = 500
}

-- UI Constants
Constants.UI = {
	CROSSHAIR_STYLES = {"Dot", "Cross", "Circle", "Square"},
	DEFAULT_CROSSHAIR_COLOR = Color3.fromRGB(255, 255, 255),
	DEFAULT_SENSITIVITY = 0.5,
	DEFAULT_FOV = 90,
	MIN_FOV = 70,
	MAX_FOV = 110
}

-- Crate Rarities
Constants.CRATE_RARITIES = {
	Common = {Color = Color3.fromRGB(155, 155, 155), Weight = 60},
	Uncommon = {Color = Color3.fromRGB(30, 255, 0), Weight = 25},
	Rare = {Color = Color3.fromRGB(0, 112, 255), Weight = 10},
	Epic = {Color = Color3.fromRGB(163, 53, 238), Weight = 4},
	Legendary = {Color = Color3.fromRGB(255, 128, 0), Weight = 1}
}

-- Map Tags (for CollectionService)
Constants.MAP_TAGS = {
	SPAWN_POINT = "SpawnPoint",
	COVER = "Cover",
	CALLOUT = "Callout",
	BOMB_SITE = "BombSite"
}

-- Network Event Names (obfuscated at runtime)
Constants.EVENTS = {
	QUEUE_JOIN = "QueueJoin",
	QUEUE_LEAVE = "QueueLeave",
	EQUIP_LOADOUT = "EquipLoadout",
	FIRE_WEAPON = "FireWeapon",
	HIT_CONFIRM = "HitConfirm",
	OPEN_CRATE = "OpenCrate",
	PURCHASE_ITEM = "PurchaseItem",
	REPORT_PLAYER = "ReportPlayer",
	HEARTBEAT = "Heartbeat"
}

Constants.FUNCTIONS = {
	GET_PROFILE = "GetProfile",
	GET_QUEUES = "GetQueues",
	GET_CRATE_ODDS = "GetCrateOdds",
	GET_MATCH_HISTORY = "GetMatchHistory"
}

-- Performance Budgets
Constants.PERFORMANCE = {
	MAX_SERVER_FRAME_TIME = 0.003, -- 3ms
	MAX_CLIENT_FRAME_TIME = 0.004, -- 4ms
	MAX_MEMORY_USAGE = 512, -- MB
	STREAMING_ENABLED = true
}

-- Season Configuration
Constants.SEASON = {
	CURRENT_SEASON = 1,
	PLACEMENT_MATCHES = 10,
	SOFT_RESET_FACTOR = 0.8, -- How much MMR to retain on reset
	SEASON_LENGTH_DAYS = 90
}

-- Matchmaking
Constants.MATCHMAKING = {
	MMR_EXPANSION_RATE = 50, -- MMR range expansion per 15 seconds
	MAX_MMR_RANGE = 400,
	MAX_WAIT_TIME = 300, -- 5 minutes
	PING_WEIGHT = 0.3,
	MMR_WEIGHT = 0.7,
	PARTY_SIZE_BONUS = 0.1
}

-- Audio
Constants.AUDIO = {
	DEFAULT_MASTER_VOLUME = 0.8,
	DEFAULT_SFX_VOLUME = 0.8,
	DEFAULT_MUSIC_VOLUME = 0.6,
	MAX_AUDIO_DISTANCE = 100
}

-- Colors
Constants.COLORS = {
	TEAM_1 = Color3.fromRGB(0, 162, 255),
	TEAM_2 = Color3.fromRGB(255, 69, 0),
	NEUTRAL = Color3.fromRGB(255, 255, 255),
	SUCCESS = Color3.fromRGB(0, 255, 0),
	WARNING = Color3.fromRGB(255, 255, 0),
	ERROR = Color3.fromRGB(255, 0, 0)
}

return Constants
