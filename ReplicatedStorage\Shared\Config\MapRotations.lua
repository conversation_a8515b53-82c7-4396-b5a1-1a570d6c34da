--!strict
--[[
	MapRotations - Map Configuration and Rotation System
	Defines all maps, their properties, and rotation schedules
]]

local MapRotations = {}

-- Map Definitions
MapRotations.MAPS = {
	Neon_District = {
		Id = "neon_district",
		Name = "Neon District",
		Description = "Futuristic cyberpunk cityscape with vertical gameplay",
		Theme = "Cyberpunk",
		Size = "Medium",
		PlayerCount = {Min = 2, Max = 8},
		Modes = {"Ranked1v1", "Ranked2v2", "Casual"},
		
		-- Spawn Configuration
		SpawnPoints = {
			Team1 = {"NeonSpawn_T1_1", "NeonSpawn_T1_2", "NeonSpawn_T1_3", "NeonSpawn_T1_4"},
			Team2 = {"NeonSpawn_T2_1", "NeonSpawn_T2_2", "NeonSpawn_T2_3", "NeonSpawn_T2_4"},
			FFA = {"NeonSpawn_FFA_1", "NeonSpawn_FFA_2", "NeonSpawn_FFA_3", "NeonSpawn_FFA_4", 
				   "NeonSpawn_FFA_5", "NeonSpawn_FFA_6", "NeonSpawn_FFA_7", "NeonSpawn_FFA_8"}
		},
		
		-- Map Features
		Features = {
			VerticalGameplay = true,
			LongSightlines = true,
			CloseQuarters = true,
			Destructibles = false,
			MovingPlatforms = true,
			WeatherEffects = false
		},
		
		-- Lighting and Atmosphere
		Lighting = {
			TimeOfDay = "Night",
			Ambient = Color3.fromRGB(25, 25, 50),
			Brightness = 0.3,
			FogColor = Color3.fromRGB(100, 50, 200),
			FogStart = 50,
			FogEnd = 500
		},
		
		-- Callouts for tactical communication
		Callouts = {
			"Upper Plaza", "Lower Plaza", "Neon Bridge", "Tech Alley", "Rooftop Garden",
			"Data Center", "Hologram Square", "Cyber Cafe", "Maintenance Tunnels", "Sky Bridge"
		},
		
		-- Map-specific settings
		Settings = {
			RespawnTime = 5,
			WeaponSpawns = false, -- No weapon spawns in ranked
			PowerupSpawns = false,
			EnvironmentalHazards = false
		},
		
		Weight = 1.0, -- Default weight in rotation
		Active = true,
		RankedPool = true,
		CasualPool = true,
		
		-- Asset Information
		Assets = {
			Thumbnail = "rbxassetid://0",
			LoadingScreen = "rbxassetid://0",
			Minimap = "rbxassetid://0"
		}
	},
	
	Glassworks = {
		Id = "glassworks",
		Name = "Glassworks",
		Description = "Industrial glass factory with strategic cover and open areas",
		Theme = "Industrial",
		Size = "Medium",
		PlayerCount = {Min = 2, Max = 8},
		Modes = {"Ranked1v1", "Ranked2v2", "Casual"},
		
		SpawnPoints = {
			Team1 = {"GlassSpawn_T1_1", "GlassSpawn_T1_2", "GlassSpawn_T1_3", "GlassSpawn_T1_4"},
			Team2 = {"GlassSpawn_T2_1", "GlassSpawn_T2_2", "GlassSpawn_T2_3", "GlassSpawn_T2_4"},
			FFA = {"GlassSpawn_FFA_1", "GlassSpawn_FFA_2", "GlassSpawn_FFA_3", "GlassSpawn_FFA_4",
				   "GlassSpawn_FFA_5", "GlassSpawn_FFA_6", "GlassSpawn_FFA_7", "GlassSpawn_FFA_8"}
		},
		
		Features = {
			VerticalGameplay = false,
			LongSightlines = true,
			CloseQuarters = true,
			Destructibles = true, -- Glass panels can be broken
			MovingPlatforms = false,
			WeatherEffects = false
		},
		
		Lighting = {
			TimeOfDay = "Day",
			Ambient = Color3.fromRGB(200, 200, 200),
			Brightness = 1.0,
			FogColor = Color3.fromRGB(180, 180, 180),
			FogStart = 100,
			FogEnd = 800
		},
		
		Callouts = {
			"Main Factory", "Glass Furnace", "Loading Dock", "Office Complex", "Warehouse",
			"Conveyor Belt", "Quality Control", "Shipping Bay", "Break Room", "Storage Loft"
		},
		
		Settings = {
			RespawnTime = 5,
			WeaponSpawns = false,
			PowerupSpawns = false,
			EnvironmentalHazards = true -- Falling glass, hot furnace areas
		},
		
		Weight = 1.0,
		Active = true,
		RankedPool = true,
		CasualPool = true,
		
		Assets = {
			Thumbnail = "rbxassetid://0",
			LoadingScreen = "rbxassetid://0",
			Minimap = "rbxassetid://0"
		}
	},
	
	-- Additional maps for variety
	Dust_Storm = {
		Id = "dust_storm",
		Name = "Dust Storm",
		Description = "Desert outpost with sandstorm weather effects",
		Theme = "Desert",
		Size = "Large",
		PlayerCount = {Min = 4, Max = 8},
		Modes = {"Casual"},
		
		SpawnPoints = {
			Team1 = {"DustSpawn_T1_1", "DustSpawn_T1_2", "DustSpawn_T1_3", "DustSpawn_T1_4"},
			Team2 = {"DustSpawn_T2_1", "DustSpawn_T2_2", "DustSpawn_T2_3", "DustSpawn_T2_4"},
			FFA = {"DustSpawn_FFA_1", "DustSpawn_FFA_2", "DustSpawn_FFA_3", "DustSpawn_FFA_4",
				   "DustSpawn_FFA_5", "DustSpawn_FFA_6", "DustSpawn_FFA_7", "DustSpawn_FFA_8"}
		},
		
		Features = {
			VerticalGameplay = false,
			LongSightlines = true,
			CloseQuarters = false,
			Destructibles = false,
			MovingPlatforms = false,
			WeatherEffects = true -- Sandstorm reduces visibility
		},
		
		Lighting = {
			TimeOfDay = "Sunset",
			Ambient = Color3.fromRGB(255, 200, 150),
			Brightness = 0.8,
			FogColor = Color3.fromRGB(200, 150, 100),
			FogStart = 30,
			FogEnd = 300
		},
		
		Callouts = {
			"Oasis", "Dune Ridge", "Outpost Alpha", "Radar Station", "Supply Depot",
			"Sandstone Cliffs", "Mirage Point", "Caravan Route", "Ancient Ruins", "Watch Tower"
		},
		
		Settings = {
			RespawnTime = 3,
			WeaponSpawns = true, -- Casual mode allows weapon spawns
			PowerupSpawns = true,
			EnvironmentalHazards = false
		},
		
		Weight = 0.8, -- Slightly less frequent in rotation
		Active = true,
		RankedPool = false, -- Not suitable for ranked due to weather effects
		CasualPool = true,
		
		Assets = {
			Thumbnail = "rbxassetid://0",
			LoadingScreen = "rbxassetid://0",
			Minimap = "rbxassetid://0"
		}
	},
	
	Arctic_Base = {
		Id = "arctic_base",
		Name = "Arctic Base",
		Description = "Frozen military installation with indoor/outdoor combat",
		Theme = "Arctic",
		Size = "Medium",
		PlayerCount = {Min = 2, Max = 6},
		Modes = {"Ranked1v1", "Ranked2v2", "Casual"},
		
		SpawnPoints = {
			Team1 = {"ArcticSpawn_T1_1", "ArcticSpawn_T1_2", "ArcticSpawn_T1_3"},
			Team2 = {"ArcticSpawn_T2_1", "ArcticSpawn_T2_2", "ArcticSpawn_T2_3"},
			FFA = {"ArcticSpawn_FFA_1", "ArcticSpawn_FFA_2", "ArcticSpawn_FFA_3", 
				   "ArcticSpawn_FFA_4", "ArcticSpawn_FFA_5", "ArcticSpawn_FFA_6"}
		},
		
		Features = {
			VerticalGameplay = true,
			LongSightlines = true,
			CloseQuarters = true,
			Destructibles = false,
			MovingPlatforms = false,
			WeatherEffects = false
		},
		
		Lighting = {
			TimeOfDay = "Day",
			Ambient = Color3.fromRGB(150, 180, 255),
			Brightness = 1.2,
			FogColor = Color3.fromRGB(200, 220, 255),
			FogStart = 80,
			FogEnd = 600
		},
		
		Callouts = {
			"Command Center", "Barracks", "Helipad", "Communications", "Armory",
			"Mess Hall", "Generator Room", "Perimeter", "Ice Cave", "Observation Deck"
		},
		
		Settings = {
			RespawnTime = 5,
			WeaponSpawns = false,
			PowerupSpawns = false,
			EnvironmentalHazards = false
		},
		
		Weight = 1.0,
		Active = true,
		RankedPool = true,
		CasualPool = true,
		
		Assets = {
			Thumbnail = "rbxassetid://0",
			LoadingScreen = "rbxassetid://0",
			Minimap = "rbxassetid://0"
		}
	}
}

-- Rotation Schedules
MapRotations.ROTATIONS = {
	Ranked1v1 = {
		Maps = {"Neon_District", "Glassworks", "Arctic_Base"},
		Weights = {1.0, 1.0, 0.9},
		RotationInterval = 3600, -- 1 hour
		ForceRotation = false
	},
	
	Ranked2v2 = {
		Maps = {"Neon_District", "Glassworks", "Arctic_Base"},
		Weights = {1.0, 1.0, 0.9},
		RotationInterval = 3600,
		ForceRotation = false
	},
	
	Casual = {
		Maps = {"Neon_District", "Glassworks", "Dust_Storm", "Arctic_Base"},
		Weights = {1.0, 1.0, 0.8, 0.9},
		RotationInterval = 1800, -- 30 minutes
		ForceRotation = false
	}
}

-- Map Voting System
MapRotations.VOTING = {
	ENABLED = true,
	VOTE_TIME = 30, -- Seconds to vote
	MAPS_PER_VOTE = 3, -- Number of maps to choose from
	REQUIRE_MAJORITY = false, -- Simple plurality wins
	ALLOW_RANDOM = true, -- Include "Random" option
	WEIGHT_RECENT_MAPS = 0.5 -- Reduce weight of recently played maps
}

-- Helper Functions
function MapRotations.GetMap(mapId: string): any?
	return MapRotations.MAPS[mapId]
end

function MapRotations.GetMapsForMode(mode: string): {any}
	local maps = {}
	for _, map in pairs(MapRotations.MAPS) do
		if table.find(map.Modes, mode) and map.Active then
			table.insert(maps, map)
		end
	end
	return maps
end

function MapRotations.GetRankedMaps(): {any}
	local maps = {}
	for _, map in pairs(MapRotations.MAPS) do
		if map.RankedPool and map.Active then
			table.insert(maps, map)
		end
	end
	return maps
end

function MapRotations.GetCasualMaps(): {any}
	local maps = {}
	for _, map in pairs(MapRotations.MAPS) do
		if map.CasualPool and map.Active then
			table.insert(maps, map)
		end
	end
	return maps
end

function MapRotations.SelectMapForMode(mode: string, recentMaps: {string}?): string?
	local rotation = MapRotations.ROTATIONS[mode]
	if not rotation then
		return nil
	end
	
	local availableMaps = {}
	local totalWeight = 0
	
	for i, mapId in ipairs(rotation.Maps) do
		local map = MapRotations.GetMap(mapId)
		if map and map.Active then
			local weight = rotation.Weights[i] or 1.0
			
			-- Reduce weight for recently played maps
			if recentMaps and table.find(recentMaps, mapId) then
				weight *= MapRotations.VOTING.WEIGHT_RECENT_MAPS
			end
			
			table.insert(availableMaps, {MapId = mapId, Weight = weight})
			totalWeight += weight
		end
	end
	
	if #availableMaps == 0 then
		return nil
	end
	
	-- Weighted random selection
	local random = math.random() * totalWeight
	local currentWeight = 0
	
	for _, mapData in ipairs(availableMaps) do
		currentWeight += mapData.Weight
		if random <= currentWeight then
			return mapData.MapId
		end
	end
	
	-- Fallback
	return availableMaps[1].MapId
end

function MapRotations.GetSpawnPoints(mapId: string, team: string | number): {string}?
	local map = MapRotations.GetMap(mapId)
	if not map then
		return nil
	end
	
	if type(team) == "number" then
		if team == 1 then
			return map.SpawnPoints.Team1
		elseif team == 2 then
			return map.SpawnPoints.Team2
		else
			return map.SpawnPoints.FFA
		end
	else
		return map.SpawnPoints[team]
	end
end

function MapRotations.IsMapSuitableForPlayerCount(mapId: string, playerCount: number): boolean
	local map = MapRotations.GetMap(mapId)
	if not map then
		return false
	end
	
	return playerCount >= map.PlayerCount.Min and playerCount <= map.PlayerCount.Max
end

function MapRotations.GetMapCallouts(mapId: string): {string}?
	local map = MapRotations.GetMap(mapId)
	return map and map.Callouts or nil
end

return MapRotations
