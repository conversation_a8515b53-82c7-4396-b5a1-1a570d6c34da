--!strict
--[[
	ReportPlayer RemoteEvent
	Handles player reporting with validation and rate limiting
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local ReportPlayer = Instance.new("RemoteEvent")
ReportPlayer.Name = "ReportPlayer"
ReportPlayer.Parent = script.Parent

-- Type definitions for the event data
export type ReportPlayerData = {
	TargetUserId: number,
	Reason: "Cheating" | "Griefing" | "Harassment" | "Inappropriate_Name" | "Other",
	Description: string?,
	MatchId: string?, -- For context
	Evidence: {string}? -- Screenshot IDs or other evidence
}

export type ReportResultData = {
	Success: boolean,
	ReportId: string?,
	Error: string?
}

return ReportPlayer
