--!strict
--[[
	Queue<PERSON>oin RemoteEvent
	Handles player queue join requests with validation
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local QueueJoin = Instance.new("RemoteEvent")
QueueJoin.Name = "QueueJoin"
QueueJoin.Parent = script.Parent

-- Type definitions for the event data
export type QueueJoinData = {
	Mode: string,
	Region: string,
	PartyMembers: {number}?,
	Preferences: {
		MaxPing: number?,
		PreferredMaps: {string}?
	}?
}

return QueueJoin
