--!strict
--[[
	CrateTables - Crate System Configuration
	Defines all crates, their contents, odds, and pity systems
]]

local CrateTables = {}

-- Rarity Configuration
CrateTables.RARITIES = {
	Common = {
		Name = "Common",
		Color = Color3.fromRGB(155, 155, 155),
		Weight = 60,
		ShardValue = 5,
		Glow = false
	},
	Uncommon = {
		Name = "Uncommon",
		Color = Color3.fromRGB(30, 255, 0),
		Weight = 25,
		ShardValue = 15,
		Glow = false
	},
	Rare = {
		Name = "Rare",
		Color = Color3.fromRGB(0, 112, 255),
		Weight = 10,
		ShardValue = 50,
		Glow = true
	},
	Epic = {
		Name = "Epic",
		Color = Color3.fromRGB(163, 53, 238),
		Weight = 4,
		ShardValue = 150,
		Glow = true
	},
	Legendary = {
		Name = "Legendary",
		Color = Color3.fromRGB(255, 128, 0),
		Weight = 1,
		ShardValue = 500,
		Glow = true
	}
}

-- Crate Definitions
CrateTables.CRATES = {
	StandardCrate = {
		Id = "standard_crate",
		Name = "Standard Crate",
		Description = "Contains common weapon skins and accessories",
		Icon = "rbxassetid://0",
		KeyRequired = false,
		CreditCost = 1000,
		PityThreshold = 10, -- Guaranteed rare+ after 10 opens without rare+
		PityIncrement = 0.1, -- 10% increase per open without rare+
		
		Items = {
			-- Common Items (60% total)
			{ItemId = "m4a1_urban", ItemType = "Skin", Rarity = "Common", Weight = 12},
			{ItemId = "ak47_woodland", ItemType = "Skin", Rarity = "Common", Weight = 12},
			{ItemId = "glock_black", ItemType = "Skin", Rarity = "Common", Weight = 12},
			{ItemId = "awp_green", ItemType = "Skin", Rarity = "Common", Weight = 12},
			{ItemId = "basic_banner", ItemType = "Banner", Rarity = "Common", Weight = 12},
			
			-- Uncommon Items (25% total)
			{ItemId = "m4a1_desert", ItemType = "Skin", Rarity = "Uncommon", Weight = 5},
			{ItemId = "ak47_red", ItemType = "Skin", Rarity = "Uncommon", Weight = 5},
			{ItemId = "glock_blue", ItemType = "Skin", Rarity = "Uncommon", Weight = 5},
			{ItemId = "awp_camo", ItemType = "Skin", Rarity = "Uncommon", Weight = 5},
			{ItemId = "victory_pose_1", ItemType = "Pose", Rarity = "Uncommon", Weight = 5},
			
			-- Rare Items (10% total)
			{ItemId = "m4a1_tiger", ItemType = "Skin", Rarity = "Rare", Weight = 2},
			{ItemId = "ak47_fire", ItemType = "Skin", Rarity = "Rare", Weight = 2},
			{ItemId = "glock_gold", ItemType = "Skin", Rarity = "Rare", Weight = 2},
			{ItemId = "awp_lightning", ItemType = "Skin", Rarity = "Rare", Weight = 2},
			{ItemId = "kill_effect_sparks", ItemType = "Effect", Rarity = "Rare", Weight = 2},
			
			-- Epic Items (4% total)
			{ItemId = "m4a1_dragon", ItemType = "Skin", Rarity = "Epic", Weight = 1},
			{ItemId = "ak47_ice", ItemType = "Skin", Rarity = "Epic", Weight = 1},
			{ItemId = "awp_neon", ItemType = "Skin", Rarity = "Epic", Weight = 1},
			{ItemId = "victory_pose_epic", ItemType = "Pose", Rarity = "Epic", Weight = 1},
			
			-- Legendary Items (1% total)
			{ItemId = "ak47_golden", ItemType = "Skin", Rarity = "Legendary", Weight = 0.5},
			{ItemId = "awp_diamond", ItemType = "Skin", Rarity = "Legendary", Weight = 0.5}
		}
	},
	
	PremiumCrate = {
		Id = "premium_crate",
		Name = "Premium Crate",
		Description = "Higher chance for rare items and exclusive skins",
		Icon = "rbxassetid://0",
		KeyRequired = true,
		KeyCost = 1,
		PityThreshold = 8,
		PityIncrement = 0.125, -- 12.5% increase per open
		
		Items = {
			-- Common Items (40% total)
			{ItemId = "m4a1_premium_1", ItemType = "Skin", Rarity = "Common", Weight = 8},
			{ItemId = "ak47_premium_1", ItemType = "Skin", Rarity = "Common", Weight = 8},
			{ItemId = "glock_premium_1", ItemType = "Skin", Rarity = "Common", Weight = 8},
			{ItemId = "awp_premium_1", ItemType = "Skin", Rarity = "Common", Weight = 8},
			{ItemId = "premium_banner_1", ItemType = "Banner", Rarity = "Common", Weight = 8},
			
			-- Uncommon Items (30% total)
			{ItemId = "m4a1_premium_2", ItemType = "Skin", Rarity = "Uncommon", Weight = 6},
			{ItemId = "ak47_premium_2", ItemType = "Skin", Rarity = "Uncommon", Weight = 6},
			{ItemId = "glock_premium_2", ItemType = "Skin", Rarity = "Uncommon", Weight = 6},
			{ItemId = "awp_premium_2", ItemType = "Skin", Rarity = "Uncommon", Weight = 6},
			{ItemId = "premium_pose_1", ItemType = "Pose", Rarity = "Uncommon", Weight = 6},
			
			-- Rare Items (20% total)
			{ItemId = "m4a1_exclusive", ItemType = "Skin", Rarity = "Rare", Weight = 4},
			{ItemId = "ak47_exclusive", ItemType = "Skin", Rarity = "Rare", Weight = 4},
			{ItemId = "glock_exclusive", ItemType = "Skin", Rarity = "Rare", Weight = 4},
			{ItemId = "awp_exclusive", ItemType = "Skin", Rarity = "Rare", Weight = 4},
			{ItemId = "premium_effect_1", ItemType = "Effect", Rarity = "Rare", Weight = 4},
			
			-- Epic Items (8% total)
			{ItemId = "m4a1_mythic", ItemType = "Skin", Rarity = "Epic", Weight = 2},
			{ItemId = "ak47_mythic", ItemType = "Skin", Rarity = "Epic", Weight = 2},
			{ItemId = "awp_mythic", ItemType = "Skin", Rarity = "Epic", Weight = 2},
			{ItemId = "premium_pose_epic", ItemType = "Pose", Rarity = "Epic", Weight = 2},
			
			-- Legendary Items (2% total)
			{ItemId = "m4a1_legendary", ItemType = "Skin", Rarity = "Legendary", Weight = 0.5},
			{ItemId = "ak47_legendary", ItemType = "Skin", Rarity = "Legendary", Weight = 0.5},
			{ItemId = "awp_legendary", ItemType = "Skin", Rarity = "Legendary", Weight = 0.5},
			{ItemId = "legendary_effect", ItemType = "Effect", Rarity = "Legendary", Weight = 0.5}
		}
	},
	
	EliteCrate = {
		Id = "elite_crate",
		Name = "Elite Crate",
		Description = "Guaranteed rare or better with high legendary chance",
		Icon = "rbxassetid://0",
		KeyRequired = true,
		KeyCost = 2,
		PityThreshold = 5,
		PityIncrement = 0.2, -- 20% increase per open
		GuaranteedMinRarity = "Rare",
		
		Items = {
			-- Rare Items (60% total)
			{ItemId = "elite_m4a1_1", ItemType = "Skin", Rarity = "Rare", Weight = 12},
			{ItemId = "elite_ak47_1", ItemType = "Skin", Rarity = "Rare", Weight = 12},
			{ItemId = "elite_awp_1", ItemType = "Skin", Rarity = "Rare", Weight = 12},
			{ItemId = "elite_glock_1", ItemType = "Skin", Rarity = "Rare", Weight = 12},
			{ItemId = "elite_banner_1", ItemType = "Banner", Rarity = "Rare", Weight = 12},
			
			-- Epic Items (30% total)
			{ItemId = "elite_m4a1_2", ItemType = "Skin", Rarity = "Epic", Weight = 6},
			{ItemId = "elite_ak47_2", ItemType = "Skin", Rarity = "Epic", Weight = 6},
			{ItemId = "elite_awp_2", ItemType = "Skin", Rarity = "Epic", Weight = 6},
			{ItemId = "elite_pose_1", ItemType = "Pose", Rarity = "Epic", Weight = 6},
			{ItemId = "elite_effect_1", ItemType = "Effect", Rarity = "Epic", Weight = 6},
			
			-- Legendary Items (10% total)
			{ItemId = "elite_m4a1_legendary", ItemType = "Skin", Rarity = "Legendary", Weight = 2},
			{ItemId = "elite_ak47_legendary", ItemType = "Skin", Rarity = "Legendary", Weight = 2},
			{ItemId = "elite_awp_legendary", ItemType = "Skin", Rarity = "Legendary", Weight = 2},
			{ItemId = "elite_pose_legendary", ItemType = "Pose", Rarity = "Legendary", Weight = 2},
			{ItemId = "elite_effect_legendary", ItemType = "Effect", Rarity = "Legendary", Weight = 2}
		}
	}
}

-- Item Database
CrateTables.ITEMS = {
	-- M4A1 Skins
	m4a1_urban = {Name = "M4A1 | Urban Camo", Description = "Standard urban camouflage", Weapon = "M4A1"},
	m4a1_desert = {Name = "M4A1 | Desert Storm", Description = "Desert warfare pattern", Weapon = "M4A1"},
	m4a1_tiger = {Name = "M4A1 | Tiger Stripe", Description = "Fierce tiger pattern", Weapon = "M4A1"},
	m4a1_dragon = {Name = "M4A1 | Dragon Scale", Description = "Mythical dragon scales", Weapon = "M4A1"},
	m4a1_premium_1 = {Name = "M4A1 | Carbon Fiber", Description = "Premium carbon fiber finish", Weapon = "M4A1"},
	m4a1_premium_2 = {Name = "M4A1 | Steel Blue", Description = "Premium steel blue coating", Weapon = "M4A1"},
	m4a1_exclusive = {Name = "M4A1 | Neon Strike", Description = "Exclusive neon design", Weapon = "M4A1"},
	m4a1_mythic = {Name = "M4A1 | Plasma Core", Description = "Mythic plasma energy", Weapon = "M4A1"},
	m4a1_legendary = {Name = "M4A1 | Ascendant", Description = "Legendary ascendant power", Weapon = "M4A1"},
	elite_m4a1_1 = {Name = "M4A1 | Elite Force", Description = "Elite tactical design", Weapon = "M4A1"},
	elite_m4a1_2 = {Name = "M4A1 | Elite Storm", Description = "Elite storm pattern", Weapon = "M4A1"},
	elite_m4a1_legendary = {Name = "M4A1 | Elite Apex", Description = "Elite apex design", Weapon = "M4A1"},
	
	-- AK47 Skins
	ak47_woodland = {Name = "AK-47 | Woodland", Description = "Forest camouflage", Weapon = "AK47"},
	ak47_red = {Name = "AK-47 | Crimson", Description = "Deep crimson finish", Weapon = "AK47"},
	ak47_fire = {Name = "AK-47 | Fire Serpent", Description = "Blazing serpent design", Weapon = "AK47"},
	ak47_ice = {Name = "AK-47 | Ice Coaled", Description = "Frozen ice coating", Weapon = "AK47"},
	ak47_golden = {Name = "AK-47 | Golden", Description = "Pure gold plating", Weapon = "AK47"},
	ak47_premium_1 = {Name = "AK-47 | Titanium", Description = "Premium titanium finish", Weapon = "AK47"},
	ak47_premium_2 = {Name = "AK-47 | Copper", Description = "Premium copper coating", Weapon = "AK47"},
	ak47_exclusive = {Name = "AK-47 | Void", Description = "Exclusive void energy", Weapon = "AK47"},
	ak47_mythic = {Name = "AK-47 | Quantum", Description = "Mythic quantum field", Weapon = "AK47"},
	ak47_legendary = {Name = "AK-47 | Infinity", Description = "Legendary infinite power", Weapon = "AK47"},
	
	-- AWP Skins
	awp_green = {Name = "AWP | Forest", Description = "Forest green finish", Weapon = "AWP"},
	awp_camo = {Name = "AWP | Digital Camo", Description = "Digital camouflage", Weapon = "AWP"},
	awp_lightning = {Name = "AWP | Lightning Strike", Description = "Electric lightning", Weapon = "AWP"},
	awp_neon = {Name = "AWP | Neon Revolution", Description = "Neon cyberpunk style", Weapon = "AWP"},
	awp_diamond = {Name = "AWP | Diamond", Description = "Pure diamond coating", Weapon = "AWP"},
	
	-- Glock Skins
	glock_black = {Name = "Glock | Tactical Black", Description = "Tactical black finish", Weapon = "Glock"},
	glock_blue = {Name = "Glock | Ocean Blue", Description = "Ocean blue coating", Weapon = "Glock"},
	glock_gold = {Name = "Glock | Gold Rush", Description = "Gold rush finish", Weapon = "Glock"},
	
	-- Banners
	basic_banner = {Name = "Basic Banner", Description = "Simple victory banner", Type = "Banner"},
	premium_banner_1 = {Name = "Premium Banner", Description = "Premium victory display", Type = "Banner"},
	elite_banner_1 = {Name = "Elite Banner", Description = "Elite championship banner", Type = "Banner"},
	
	-- Victory Poses
	victory_pose_1 = {Name = "Champion Pose", Description = "Victory champion stance", Type = "Pose"},
	premium_pose_1 = {Name = "Premium Pose", Description = "Premium victory pose", Type = "Pose"},
	premium_pose_epic = {Name = "Epic Pose", Description = "Epic victory celebration", Type = "Pose"},
	elite_pose_1 = {Name = "Elite Pose", Description = "Elite victory stance", Type = "Pose"},
	elite_pose_legendary = {Name = "Legendary Pose", Description = "Legendary victory pose", Type = "Pose"},
	
	-- Kill Effects
	kill_effect_sparks = {Name = "Sparks Effect", Description = "Electric sparks on kill", Type = "Effect"},
	premium_effect_1 = {Name = "Premium Effect", Description = "Premium kill effect", Type = "Effect"},
	elite_effect_1 = {Name = "Elite Effect", Description = "Elite kill effect", Type = "Effect"},
	legendary_effect = {Name = "Legendary Effect", Description = "Legendary kill effect", Type = "Effect"},
	elite_effect_legendary = {Name = "Elite Legendary Effect", Description = "Elite legendary effect", Type = "Effect"}
}

-- Pity System Configuration
CrateTables.PITY_SYSTEM = {
	ENABLED = true,
	TRACK_PER_CRATE_TYPE = true,
	RESET_ON_RARE_OR_BETTER = true,
	MAX_PITY_MULTIPLIER = 5.0, -- Maximum 5x increase in rare+ chance
	
	-- Pity Notifications
	NOTIFY_PITY_BUILDUP = true,
	PITY_WARNING_THRESHOLD = 0.5, -- Notify when pity reaches 50% of threshold
}

-- Helper Functions
function CrateTables.GetCrateData(crateId: string): any?
	return CrateTables.CRATES[crateId]
end

function CrateTables.GetItemData(itemId: string): any?
	return CrateTables.ITEMS[itemId]
end

function CrateTables.GetRarityData(rarity: string): any?
	return CrateTables.RARITIES[rarity]
end

function CrateTables.CalculateDropOdds(crateId: string, pityCounter: number?): {[string]: number}
	local crateData = CrateTables.GetCrateData(crateId)
	if not crateData then
		return {}
	end
	
	local pity = pityCounter or 0
	local pityMultiplier = 1 + (pity * (crateData.PityIncrement or 0))
	
	local odds = {}
	local totalWeight = 0
	
	-- Calculate total weight
	for _, item in ipairs(crateData.Items) do
		local weight = item.Weight
		
		-- Apply pity multiplier to rare+ items
		local rarityData = CrateTables.GetRarityData(item.Rarity)
		if rarityData and rarityData.Weight <= 10 then -- Rare or better
			weight *= pityMultiplier
		end
		
		totalWeight += weight
	end
	
	-- Calculate individual odds
	for _, item in ipairs(crateData.Items) do
		local weight = item.Weight
		
		-- Apply pity multiplier to rare+ items
		local rarityData = CrateTables.GetRarityData(item.Rarity)
		if rarityData and rarityData.Weight <= 10 then -- Rare or better
			weight *= pityMultiplier
		end
		
		odds[item.ItemId] = (weight / totalWeight) * 100
	end
	
	return odds
end

function CrateTables.GetRarityOdds(crateId: string, pityCounter: number?): {[string]: number}
	local itemOdds = CrateTables.CalculateDropOdds(crateId, pityCounter)
	local rarityOdds = {}
	
	for itemId, odds in pairs(itemOdds) do
		local crateData = CrateTables.GetCrateData(crateId)
		if crateData then
			for _, item in ipairs(crateData.Items) do
				if item.ItemId == itemId then
					local rarity = item.Rarity
					rarityOdds[rarity] = (rarityOdds[rarity] or 0) + odds
					break
				end
			end
		end
	end
	
	return rarityOdds
end

return CrateTables
