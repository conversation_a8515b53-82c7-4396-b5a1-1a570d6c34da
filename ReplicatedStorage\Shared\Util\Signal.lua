--!strict
--[[
	Signal - Typed Signal Implementation
	A lightweight, typed signal class for event handling
]]

local Signal = {}
Signal.__index = Signal

export type Connection = {
	Disconnect: (self: Connection) -> ()
}

export type Signal<T...> = {
	Connect: (self: Signal<T...>, callback: (T...) -> ()) -> Connection,
	ConnectOnce: (self: Signal<T...>, callback: (T...) -> ()) -> Connection,
	Fire: (self: Signal<T...>, T...) -> (),
	Wait: (self: Signal<T...>) -> T...,
	Destroy: (self: Signal<T...>) -> ()
}

type SignalImpl<T...> = {
	_connections: {ConnectionImpl},
	_destroyed: boolean
}

type ConnectionImpl = {
	_callback: (...any) -> (),
	_signal: SignalImpl<...any>,
	_connected: boolean,
	_once: boolean
}

local ConnectionMT = {}
ConnectionMT.__index = ConnectionMT

function ConnectionMT:Disconnect()
	if not self._connected then
		return
	end
	
	self._connected = false
	
	local connections = self._signal._connections
	local index = table.find(connections, self)
	if index then
		table.remove(connections, index)
	end
end

function Signal.new<T...>(): Signal<T...>
	local self = {
		_connections = {},
		_destroyed = false
	}
	
	return setmetatable(self, Signal) :: any
end

function Signal:Connect<T...>(callback: (T...) -> ()): Connection
	assert(not self._destroyed, "Cannot connect to destroyed signal")
	assert(type(callback) == "function", "Callback must be a function")
	
	local connection = {
		_callback = callback,
		_signal = self,
		_connected = true,
		_once = false
	}
	
	setmetatable(connection, ConnectionMT)
	table.insert(self._connections, connection)
	
	return connection :: any
end

function Signal:ConnectOnce<T...>(callback: (T...) -> ()): Connection
	assert(not self._destroyed, "Cannot connect to destroyed signal")
	assert(type(callback) == "function", "Callback must be a function")
	
	local connection = {
		_callback = callback,
		_signal = self,
		_connected = true,
		_once = true
	}
	
	setmetatable(connection, ConnectionMT)
	table.insert(self._connections, connection)
	
	return connection :: any
end

function Signal:Fire<T...>(...: T...)
	if self._destroyed then
		return
	end
	
	-- Copy connections to avoid issues with disconnection during firing
	local connections = {}
	for _, connection in ipairs(self._connections) do
		if connection._connected then
			table.insert(connections, connection)
		end
	end
	
	for _, connection in ipairs(connections) do
		if connection._connected then
			task.spawn(connection._callback, ...)
			
			if connection._once then
				connection:Disconnect()
			end
		end
	end
end

function Signal:Wait<T...>(): T...
	assert(not self._destroyed, "Cannot wait on destroyed signal")
	
	local thread = coroutine.running()
	local connection
	
	connection = self:ConnectOnce(function(...)
		task.spawn(thread, ...)
	end)
	
	return coroutine.yield()
end

function Signal:Destroy()
	if self._destroyed then
		return
	end
	
	self._destroyed = true
	
	for _, connection in ipairs(self._connections) do
		connection:Disconnect()
	end
	
	table.clear(self._connections)
end

return Signal
