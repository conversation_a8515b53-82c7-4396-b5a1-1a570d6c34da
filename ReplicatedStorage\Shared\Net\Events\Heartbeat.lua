--!strict
--[[
	Heartbeat RemoteEvent
	Anti-exploit heartbeat system for detecting anomalies
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local Heartbeat = Instance.new("RemoteEvent")
Heartbeat.Name = "Heartbeat"
Heartbeat.Parent = script.Parent

-- Type definitions for the event data
export type HeartbeatData = {
	Timestamp: number,
	Position: Vector3?,
	Health: number?,
	Ping: number?,
	FPS: number?,
	ClientTime: number
}

return Heartbeat
