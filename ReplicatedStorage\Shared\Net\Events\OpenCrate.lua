--!strict
--[[
	OpenCrate RemoteEvent
	Handles crate opening requests with validation
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local OpenCrate = Instance.new("RemoteEvent")
OpenCrate.Name = "OpenCrate"
OpenCrate.Parent = script.Parent

-- Type definitions for the event data
export type OpenCrateData = {
	CrateId: string,
	UseKey: boolean?,
	Quantity: number? -- For bulk opening
}

export type CrateResultData = {
	Success: boolean,
	Items: {{
		ItemId: string,
		ItemType: string,
		Rarity: string,
		IsNew: boolean,
		ShardsAwarded: number?
	}}?,
	Error: string?,
	PityCounter: number?
}

return OpenCrate
