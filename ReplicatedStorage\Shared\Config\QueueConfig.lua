--!strict
--[[
	QueueConfig - Matchmaking Queue Configuration
	Defines queue parameters, regions, and matchmaking rules
]]

local QueueConfig = {}

-- Available Regions
QueueConfig.REGIONS = {
	{
		Id = "OCE",
		Name = "Oceania",
		DisplayName = "Oceania",
		Flag = "🇦🇺",
		Servers = {"Sydney", "Melbourne"},
		DefaultPing = 50
	},
	{
		Id = "NAE",
		Name = "North America East",
		DisplayName = "NA East",
		Flag = "🇺🇸",
		Servers = {"Virginia", "Ohio"},
		DefaultPing = 30
	},
	{
		Id = "EU",
		Name = "Europe",
		DisplayName = "Europe",
		Flag = "🇪🇺",
		Servers = {"Frankfurt", "London"},
		DefaultPing = 40
	}
}

-- Queue Modes
QueueConfig.MODES = {
	Ranked1v1 = {
		Name = "Ranked 1v1",
		Description = "Competitive 1v1 duels",
		TeamSize = 1,
		MaxPlayers = 2,
		RankedMode = true,
		RequiredLevel = 5,
		AllowParties = false,
		EstimatedTime = 60, -- seconds
		Icon = "rbxassetid://0"
	},
	Ranked2v2 = {
		Name = "Ranked 2v2",
		Description = "Competitive team battles",
		TeamSize = 2,
		MaxPlayers = 4,
		RankedMode = true,
		RequiredLevel = 10,
		AllowParties = true,
		MaxPartySize = 2,
		EstimatedTime = 90,
		Icon = "rbxassetid://0"
	},
	Casual = {
		Name = "Casual",
		Description = "Quick casual matches",
		TeamSize = 4,
		MaxPlayers = 8,
		RankedMode = false,
		RequiredLevel = 1,
		AllowParties = true,
		MaxPartySize = 4,
		EstimatedTime = 30,
		Icon = "rbxassetid://0"
	}
}

-- Matchmaking Parameters
QueueConfig.MATCHMAKING = {
	-- MMR Expansion
	INITIAL_MMR_RANGE = 100, -- Starting MMR search range
	MMR_EXPANSION_RATE = 50, -- MMR range increase per interval
	MMR_EXPANSION_INTERVAL = 15, -- Seconds between expansions
	MAX_MMR_RANGE = 400, -- Maximum MMR search range
	
	-- Wait Time Limits
	MAX_WAIT_TIME = 300, -- 5 minutes maximum wait
	PRIORITY_WAIT_TIME = 180, -- 3 minutes before priority matching
	
	-- Quality Factors
	MMR_WEIGHT = 0.7, -- How much MMR difference matters
	PING_WEIGHT = 0.2, -- How much ping difference matters
	WAIT_TIME_WEIGHT = 0.1, -- How much wait time matters
	
	-- Ping Constraints
	MAX_PING_DIFFERENCE = 100, -- Maximum ping difference between players
	PREFERRED_PING_THRESHOLD = 80, -- Preferred maximum ping
	
	-- Party Bonuses
	PARTY_MMR_BONUS = 50, -- MMR bonus for parties vs solo players
	PARTY_PRIORITY_BONUS = 0.1, -- Priority bonus for parties
	
	-- Special Rules
	AVOID_RECENT_OPPONENTS = true,
	RECENT_OPPONENT_WINDOW = 300, -- 5 minutes
	SKILL_UNCERTAINTY_FACTOR = 0.3, -- How much RD affects matching
	
	-- Regional Preferences
	PREFER_SAME_REGION = true,
	CROSS_REGION_DELAY = 120, -- Seconds before allowing cross-region
	CROSS_REGION_PING_PENALTY = 50 -- Extra ping penalty for cross-region
}

-- Queue Limits and Restrictions
QueueConfig.LIMITS = {
	-- Per-player limits
	MAX_CONCURRENT_QUEUES = 1, -- Player can only be in one queue
	QUEUE_COOLDOWN = 5, -- Seconds before re-queueing after leaving
	DODGE_PENALTY = 60, -- Seconds penalty for dodging matches
	MAX_DODGES_PER_HOUR = 3,
	
	-- System limits
	MAX_QUEUE_SIZE = 1000, -- Maximum players per queue
	QUEUE_CLEANUP_INTERVAL = 30, -- Seconds between queue cleanup
	STALE_TICKET_TIMEOUT = 600, -- 10 minutes before ticket expires
	
	-- Anti-abuse
	MIN_LEVEL_RANKED = 5, -- Minimum level for ranked queues
	PLACEMENT_MATCH_RESTRICTION = false, -- Allow placement players together
	SMURF_DETECTION_ENABLED = true
}

-- Queue Priorities
QueueConfig.PRIORITIES = {
	-- Player type priorities (higher = more priority)
	RETURNING_PLAYER = 1.2, -- Players returning after break
	PREMIUM_PLAYER = 1.1, -- Players with premium status
	LONG_WAIT_PLAYER = 1.5, -- Players waiting longer than threshold
	PARTY_LEADER = 1.1, -- Party leaders get slight priority
	
	-- Time-based priorities
	PEAK_HOURS = {
		{Start = 18, End = 23, Multiplier = 1.0}, -- 6 PM - 11 PM
		{Start = 12, End = 14, Multiplier = 0.9}, -- 12 PM - 2 PM
		{Start = 0, End = 6, Multiplier = 0.7} -- Midnight - 6 AM
	}
}

-- Queue Health Monitoring
QueueConfig.MONITORING = {
	-- Health thresholds
	HEALTHY_WAIT_TIME = 60, -- Seconds
	WARNING_WAIT_TIME = 120,
	CRITICAL_WAIT_TIME = 240,
	
	-- Population thresholds
	MIN_HEALTHY_POPULATION = 20,
	MIN_WARNING_POPULATION = 10,
	MIN_CRITICAL_POPULATION = 5,
	
	-- Auto-adjustments
	AUTO_ADJUST_PARAMETERS = true,
	LOW_POP_MMR_EXPANSION = 2.0, -- Multiply expansion rate when low pop
	LOW_POP_PING_RELAXATION = 1.5, -- Relax ping requirements when low pop
	
	-- Notifications
	NOTIFY_LONG_QUEUES = true,
	LONG_QUEUE_THRESHOLD = 180, -- 3 minutes
	QUEUE_STATUS_UPDATE_INTERVAL = 15 -- Seconds
}

-- Cross-Platform Considerations
QueueConfig.PLATFORM = {
	ALLOW_CROSS_PLATFORM = true,
	PLATFORM_PREFERENCES = {
		PC = {AimAssist = false, InputAdvantage = 1.0},
		Console = {AimAssist = true, InputAdvantage = 0.9},
		Mobile = {AimAssist = true, InputAdvantage = 0.8}
	},
	
	-- Ranked restrictions
	RANKED_CROSS_PLATFORM = false, -- Disable cross-platform in ranked
	CASUAL_CROSS_PLATFORM = true
}

-- Helper Functions
function QueueConfig.GetModeConfig(mode: string): any?
	return QueueConfig.MODES[mode]
end

function QueueConfig.GetRegionConfig(regionId: string): any?
	for _, region in ipairs(QueueConfig.REGIONS) do
		if region.Id == regionId then
			return region
		end
	end
	return nil
end

function QueueConfig.IsValidMode(mode: string): boolean
	return QueueConfig.MODES[mode] ~= nil
end

function QueueConfig.IsValidRegion(regionId: string): boolean
	return QueueConfig.GetRegionConfig(regionId) ~= nil
end

function QueueConfig.CanPlayerQueue(playerLevel: number, mode: string): (boolean, string?)
	local modeConfig = QueueConfig.GetModeConfig(mode)
	if not modeConfig then
		return false, "Invalid queue mode"
	end
	
	if playerLevel < modeConfig.RequiredLevel then
		return false, `Level ${modeConfig.RequiredLevel} required for ${modeConfig.Name}`
	end
	
	return true, nil
end

function QueueConfig.GetEstimatedWaitTime(mode: string, region: string, currentPopulation: number): number
	local modeConfig = QueueConfig.GetModeConfig(mode)
	if not modeConfig then
		return 300 -- Default to max wait time
	end
	
	local baseTime = modeConfig.EstimatedTime
	
	-- Adjust based on population
	local populationFactor = math.max(0.5, math.min(2.0, 50 / math.max(1, currentPopulation)))
	
	-- Adjust based on time of day
	local hour = os.date("*t").hour
	local timeFactor = 1.0
	for _, period in ipairs(QueueConfig.PRIORITIES.PEAK_HOURS) do
		if hour >= period.Start and hour <= period.End then
			timeFactor = 1 / period.Multiplier
			break
		end
	end
	
	return math.floor(baseTime * populationFactor * timeFactor)
end

function QueueConfig.GetMMRRange(waitTime: number): number
	local expansions = math.floor(waitTime / QueueConfig.MATCHMAKING.MMR_EXPANSION_INTERVAL)
	local range = QueueConfig.MATCHMAKING.INITIAL_MMR_RANGE + 
		(expansions * QueueConfig.MATCHMAKING.MMR_EXPANSION_RATE)
	
	return math.min(range, QueueConfig.MATCHMAKING.MAX_MMR_RANGE)
end

return QueueConfig
