--!strict
--[[
	GetProfile RemoteFunction
	Retrieves player profile data
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteFunction
local GetProfile = Instance.new("RemoteFunction")
GetProfile.Name = "GetProfile"
GetProfile.Parent = script.Parent

-- Type definitions for the function data
export type ProfileRequest = {
	IncludeStatistics: boolean?,
	IncludeMatchHistory: boolean?,
	IncludeInventory: boolean?
}

export type ProfileResponse = {
	Success: boolean,
	Profile: any?, -- PlayerProfile type from Types.lua
	Error: string?
}

return GetProfile
