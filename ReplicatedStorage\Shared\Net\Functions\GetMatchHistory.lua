--!strict
--[[
	GetMatchHistory RemoteFunction
	Retrieves player match history with filtering options
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteFunction
local GetMatchHistory = Instance.new("RemoteFunction")
GetMatchHistory.Name = "GetMatchHistory"
GetMatchHistory.Parent = script.Parent

-- Type definitions for the function data
export type MatchHistoryRequest = {
	Limit: number?, -- Max matches to return (default 20)
	Offset: number?, -- For pagination
	Mode: string?, -- Filter by mode
	Season: number?, -- Filter by season
	IncludeStats: boolean? -- Include detailed stats
}

export type MatchSummary = {
	MatchId: string,
	Mode: string,
	Map: string,
	Result: "Win" | "Loss" | "Draw",
	Score: string, -- e.g., "16-14"
	Duration: number,
	CompletedAt: number,
	MMRChange: number?,
	PlayerStats: {
		Kills: number,
		Deaths: number,
		Damage: number,
		Headshots: number,
		KDR: number,
		ADR: number -- Average damage per round
	}?
}

export type MatchHistoryResponse = {
	Success: boolean,
	Matches: {MatchSummary}?,
	TotalMatches: number?,
	HasMore: boolean?,
	Error: string?
}

return GetMatchHistory
