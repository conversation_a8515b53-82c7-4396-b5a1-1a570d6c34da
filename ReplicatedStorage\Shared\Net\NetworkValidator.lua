--!strict
--[[
	NetworkValidator - Network Input Validation
	Provides validation functions for all network requests
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Constants = require(ReplicatedStorage.Shared.Constants)

local NetworkValidator = {}

-- Rate limiting storage (server-side only)
local rateLimits = {}

-- Validation helper functions
local function isValidString(value: any, maxLength: number?): boolean
	if type(value) ~= "string" then
		return false
	end
	
	if maxLength and #value > maxLength then
		return false
	end
	
	return true
end

local function isValidNumber(value: any, min: number?, max: number?): boolean
	if type(value) ~= "number" then
		return false
	end
	
	if min and value < min then
		return false
	end
	
	if max and value > max then
		return false
	end
	
	return true
end

local function isValidVector3(value: any): boolean
	return typeof(value) == "Vector3"
end

local function isValidUserId(value: any): boolean
	return type(value) == "number" and value > 0 and value == math.floor(value)
end

-- Rate limiting functions
function NetworkValidator.CheckRateLimit(userId: number, eventName: string): boolean
	local limit = Constants.RATE_LIMITS[eventName]
	if not limit then
		return true -- No limit defined
	end
	
	local now = tick()
	local userLimits = rateLimits[userId] or {}
	local eventLimits = userLimits[eventName] or {requests = 0, windowStart = now}
	
	-- Reset window if expired
	if now - eventLimits.windowStart >= limit.Window then
		eventLimits.requests = 0
		eventLimits.windowStart = now
	end
	
	-- Check if limit exceeded
	if eventLimits.requests >= limit.Requests then
		return false
	end
	
	-- Increment counter
	eventLimits.requests += 1
	userLimits[eventName] = eventLimits
	rateLimits[userId] = userLimits
	
	return true
end

-- Queue validation
function NetworkValidator.ValidateQueueJoin(data: any): (boolean, string?)
	if type(data) ~= "table" then
		return false, "Invalid data format"
	end
	
	-- Validate mode
	if not isValidString(data.Mode, 20) then
		return false, "Invalid mode"
	end
	
	-- Validate region
	if not isValidString(data.Region, 10) then
		return false, "Invalid region"
	end
	
	-- Validate party members if provided
	if data.PartyMembers then
		if type(data.PartyMembers) ~= "table" then
			return false, "Invalid party members format"
		end
		
		if #data.PartyMembers > 4 then
			return false, "Too many party members"
		end
		
		for _, memberId in ipairs(data.PartyMembers) do
			if not isValidUserId(memberId) then
				return false, "Invalid party member ID"
			end
		end
	end
	
	-- Validate preferences if provided
	if data.Preferences then
		if type(data.Preferences) ~= "table" then
			return false, "Invalid preferences format"
		end
		
		if data.Preferences.MaxPing and not isValidNumber(data.Preferences.MaxPing, 0, 1000) then
			return false, "Invalid max ping"
		end
		
		if data.Preferences.PreferredMaps then
			if type(data.Preferences.PreferredMaps) ~= "table" then
				return false, "Invalid preferred maps format"
			end
			
			for _, mapId in ipairs(data.Preferences.PreferredMaps) do
				if not isValidString(mapId, 50) then
					return false, "Invalid map ID"
				end
			end
		end
	end
	
	return true, nil
end

-- Weapon fire validation
function NetworkValidator.ValidateFireWeapon(data: any): (boolean, string?)
	if type(data) ~= "table" then
		return false, "Invalid data format"
	end
	
	-- Validate weapon ID
	if not isValidString(data.WeaponId, 50) then
		return false, "Invalid weapon ID"
	end
	
	-- Validate origin
	if not isValidVector3(data.Origin) then
		return false, "Invalid origin"
	end
	
	-- Validate direction
	if not isValidVector3(data.Direction) then
		return false, "Invalid direction"
	end
	
	-- Check direction is normalized
	if math.abs(data.Direction.Magnitude - 1) > 0.1 then
		return false, "Direction not normalized"
	end
	
	-- Validate timestamp
	if not isValidNumber(data.Timestamp, 0) then
		return false, "Invalid timestamp"
	end
	
	-- Check timestamp is recent (within lag compensation window)
	local now = tick()
	if math.abs(now - data.Timestamp) > Constants.TIMING.LAG_COMPENSATION_WINDOW then
		return false, "Timestamp too old"
	end
	
	-- Validate shot number
	if not isValidNumber(data.ShotNumber, 1, 1000) then
		return false, "Invalid shot number"
	end
	
	-- Validate spread if provided
	if data.Spread and not isValidNumber(data.Spread, 0, 1) then
		return false, "Invalid spread value"
	end
	
	return true, nil
end

-- Loadout validation
function NetworkValidator.ValidateEquipLoadout(data: any): (boolean, string?)
	if type(data) ~= "table" then
		return false, "Invalid data format"
	end
	
	-- Validate loadout slot
	if not isValidString(data.LoadoutSlot, 20) then
		return false, "Invalid loadout slot"
	end
	
	local validSlots = {"Primary", "Secondary", "Melee", "Grenade", "Utility"}
	if not table.find(validSlots, data.LoadoutSlot) then
		return false, "Unknown loadout slot"
	end
	
	-- Validate weapon ID if provided
	if data.WeaponId and not isValidString(data.WeaponId, 50) then
		return false, "Invalid weapon ID"
	end
	
	-- Validate attachments if provided
	if data.Attachments then
		if type(data.Attachments) ~= "table" then
			return false, "Invalid attachments format"
		end
		
		for slot, attachmentId in pairs(data.Attachments) do
			if not isValidString(slot, 20) or not isValidString(attachmentId, 50) then
				return false, "Invalid attachment data"
			end
		end
	end
	
	-- Validate skin ID if provided
	if data.SkinId and not isValidString(data.SkinId, 50) then
		return false, "Invalid skin ID"
	end
	
	return true, nil
end

-- Crate opening validation
function NetworkValidator.ValidateOpenCrate(data: any): (boolean, string?)
	if type(data) ~= "table" then
		return false, "Invalid data format"
	end
	
	-- Validate crate ID
	if not isValidString(data.CrateId, 50) then
		return false, "Invalid crate ID"
	end
	
	-- Validate use key flag
	if data.UseKey ~= nil and type(data.UseKey) ~= "boolean" then
		return false, "Invalid use key flag"
	end
	
	-- Validate quantity
	if data.Quantity and not isValidNumber(data.Quantity, 1, 100) then
		return false, "Invalid quantity"
	end
	
	return true, nil
end

-- Purchase validation
function NetworkValidator.ValidatePurchaseItem(data: any): (boolean, string?)
	if type(data) ~= "table" then
		return false, "Invalid data format"
	end
	
	-- Validate item ID
	if not isValidString(data.ItemId, 50) then
		return false, "Invalid item ID"
	end
	
	-- Validate item type
	local validTypes = {"Weapon", "Skin", "Crate", "Key", "Utility"}
	if not table.find(validTypes, data.ItemType) then
		return false, "Invalid item type"
	end
	
	-- Validate currency
	local validCurrencies = {"Credits", "Keys", "Shards", "Robux"}
	if not table.find(validCurrencies, data.Currency) then
		return false, "Invalid currency"
	end
	
	-- Validate quantity
	if data.Quantity and not isValidNumber(data.Quantity, 1, 1000) then
		return false, "Invalid quantity"
	end
	
	return true, nil
end

-- Report validation
function NetworkValidator.ValidateReportPlayer(data: any): (boolean, string?)
	if type(data) ~= "table" then
		return false, "Invalid data format"
	end
	
	-- Validate target user ID
	if not isValidUserId(data.TargetUserId) then
		return false, "Invalid target user ID"
	end
	
	-- Validate reason
	local validReasons = {"Cheating", "Griefing", "Harassment", "Inappropriate_Name", "Other"}
	if not table.find(validReasons, data.Reason) then
		return false, "Invalid report reason"
	end
	
	-- Validate description if provided
	if data.Description and not isValidString(data.Description, 500) then
		return false, "Invalid description"
	end
	
	-- Validate match ID if provided
	if data.MatchId and not isValidString(data.MatchId, 100) then
		return false, "Invalid match ID"
	end
	
	-- Validate evidence if provided
	if data.Evidence then
		if type(data.Evidence) ~= "table" then
			return false, "Invalid evidence format"
		end
		
		if #data.Evidence > 10 then
			return false, "Too many evidence items"
		end
		
		for _, evidenceId in ipairs(data.Evidence) do
			if not isValidString(evidenceId, 100) then
				return false, "Invalid evidence ID"
			end
		end
	end
	
	return true, nil
end

-- Heartbeat validation
function NetworkValidator.ValidateHeartbeat(data: any): (boolean, string?)
	if type(data) ~= "table" then
		return false, "Invalid data format"
	end
	
	-- Validate timestamp
	if not isValidNumber(data.Timestamp, 0) then
		return false, "Invalid timestamp"
	end
	
	-- Validate position if provided
	if data.Position and not isValidVector3(data.Position) then
		return false, "Invalid position"
	end
	
	-- Validate health if provided
	if data.Health and not isValidNumber(data.Health, 0, 100) then
		return false, "Invalid health"
	end
	
	-- Validate ping if provided
	if data.Ping and not isValidNumber(data.Ping, 0, 2000) then
		return false, "Invalid ping"
	end
	
	-- Validate FPS if provided
	if data.FPS and not isValidNumber(data.FPS, 1, 240) then
		return false, "Invalid FPS"
	end
	
	-- Validate client time
	if not isValidNumber(data.ClientTime, 0) then
		return false, "Invalid client time"
	end
	
	return true, nil
end

return NetworkValidator
