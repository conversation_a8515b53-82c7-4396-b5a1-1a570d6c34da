--!strict
--[[
	FireWeapon RemoteEvent
	Handles weapon fire requests with anti-exploit validation
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local FireWeapon = Instance.new("RemoteEvent")
FireWeapon.Name = "FireWeapon"
FireWeapon.Parent = script.Parent

-- Type definitions for the event data
export type FireWeaponData = {
	WeaponId: string,
	Origin: Vector3,
	Direction: Vector3,
	Timestamp: number,
	ShotNumber: number, -- For recoil pattern tracking
	Spread: number? -- Client-calculated spread for validation
}

return FireWeapon
