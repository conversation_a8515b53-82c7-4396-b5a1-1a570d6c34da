--!strict
--[[
	QueueLeave RemoteEvent
	Handles player queue leave requests
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Create the RemoteEvent
local QueueLeave = Instance.new("RemoteEvent")
QueueLeave.Name = "QueueLeave"
QueueLeave.Parent = script.Parent

-- Type definitions for the event data
export type QueueLeaveData = {
	Reason: "Manual" | "Timeout" | "MatchFound" | "Error"?
}

return QueueLeave
