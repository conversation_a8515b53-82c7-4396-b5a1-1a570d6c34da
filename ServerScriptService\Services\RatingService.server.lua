--!strict
--[[
	RatingService - Glicko-2 Rating System
	Implements the Glicko-2 rating system for competitive matchmaking
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

local Types = require(ReplicatedStorage.Shared.Types)
local Constants = require(ReplicatedStorage.Shared.Constants)
local RankConfig = require(ReplicatedStorage.Shared.Config.RankConfig)

local RatingService = {}

-- Glicko-2 constants
local GLICKO_SCALE = 173.7178 -- Conversion factor from Glicko to Glicko-2
local TAU = RankConfig.MMR_CONFIG.TAU
local EPSILON = RankConfig.MMR_CONFIG.EPSILON

-- Helper functions for Glicko-2 calculations
local function g(phi: number): number
	return 1 / math.sqrt(1 + 3 * phi^2 / math.pi^2)
end

local function E(mu: number, mu_j: number, phi_j: number): number
	return 1 / (1 + math.exp(-g(phi_j) * (mu - mu_j)))
end

local function f(x: number, delta: number, phi: number, v: number, a: number): number
	local ex = math.exp(x)
	local phi2 = phi^2
	local denom1 = phi2 + v + ex
	local denom2 = (phi2 + v + ex)^2
	
	return (ex * (delta^2 - phi2 - v - ex)) / (2 * denom2) - (x - a) / TAU^2
end

-- Convert rating to Glicko-2 scale
local function toGlicko2(rating: number, rd: number): (number, number)
	local mu = (rating - 1500) / GLICKO_SCALE
	local phi = rd / GLICKO_SCALE
	return mu, phi
end

-- Convert from Glicko-2 scale back to rating
local function fromGlicko2(mu: number, phi: number): (number, number)
	local rating = mu * GLICKO_SCALE + 1500
	local rd = phi * GLICKO_SCALE
	return rating, rd
end

-- Calculate new volatility using Illinois algorithm
local function calculateNewVolatility(sigma: number, delta: number, phi: number, v: number): number
	local a = math.log(sigma^2)
	local A = a
	local B: number
	
	if delta^2 > phi^2 + v then
		B = math.log(delta^2 - phi^2 - v)
	else
		local k = 1
		while f(a - k * TAU, delta, phi, v, a) < 0 do
			k += 1
		end
		B = a - k * TAU
	end
	
	local fA = f(A, delta, phi, v, a)
	local fB = f(B, delta, phi, v, a)
	
	while math.abs(B - A) > EPSILON do
		local C = A + (A - B) * fA / (fB - fA)
		local fC = f(C, delta, phi, v, a)
		
		if fC * fB < 0 then
			A = B
			fA = fB
		else
			fA = fA / 2
		end
		
		B = C
		fB = fC
	end
	
	return math.exp(A / 2)
end

-- Main Glicko-2 update function
function RatingService.UpdateRating(
	playerRating: number,
	playerRD: number,
	playerVolatility: number,
	opponentRating: number,
	opponentRD: number,
	score: number -- 1 for win, 0.5 for draw, 0 for loss
): (number, number, number)
	
	-- Convert to Glicko-2 scale
	local mu, phi = toGlicko2(playerRating, playerRD)
	local mu_j, phi_j = toGlicko2(opponentRating, opponentRD)
	
	-- Step 1: Calculate v (estimated variance)
	local g_phi_j = g(phi_j)
	local E_mu_mu_j_phi_j = E(mu, mu_j, phi_j)
	local v = 1 / (g_phi_j^2 * E_mu_mu_j_phi_j * (1 - E_mu_mu_j_phi_j))
	
	-- Step 2: Calculate delta (improvement in rating)
	local delta = v * g_phi_j * (score - E_mu_mu_j_phi_j)
	
	-- Step 3: Calculate new volatility
	local newVolatility = calculateNewVolatility(playerVolatility, delta, phi, v)
	
	-- Step 4: Update rating deviation
	local phiStar = math.sqrt(phi^2 + newVolatility^2)
	local newPhi = 1 / math.sqrt(1 / phiStar^2 + 1 / v)
	
	-- Step 5: Update rating
	local newMu = mu + newPhi^2 * g_phi_j * (score - E_mu_mu_j_phi_j)
	
	-- Convert back to original scale
	local newRating, newRD = fromGlicko2(newMu, newPhi)
	
	-- Apply bounds
	newRating = math.max(RankConfig.MMR_CONFIG.MIN_RATING, 
		math.min(RankConfig.MMR_CONFIG.MAX_RATING, newRating))
	newRD = math.max(RankConfig.MMR_CONFIG.MIN_DEVIATION,
		math.min(RankConfig.MMR_CONFIG.MAX_DEVIATION, newRD))
	
	return newRating, newRD, newVolatility
end

-- Update rating for team vs team matches
function RatingService.UpdateTeamRating(
	team1Players: {{rating: number, rd: number, volatility: number}},
	team2Players: {{rating: number, rd: number, volatility: number}},
	team1Score: number -- 1 for team1 win, 0.5 for draw, 0 for team1 loss
): ({{rating: number, rd: number, volatility: number}}, {{rating: number, rd: number, volatility: number}})
	
	-- Calculate team ratings as weighted averages
	local function calculateTeamRating(players: {{rating: number, rd: number, volatility: number}}): (number, number)
		local totalWeight = 0
		local weightedRating = 0
		local weightedRD = 0
		
		for _, player in ipairs(players) do
			local weight = 1 / (player.rd^2) -- Weight by inverse of RD squared
			totalWeight += weight
			weightedRating += player.rating * weight
			weightedRD += player.rd * weight
		end
		
		return weightedRating / totalWeight, weightedRD / totalWeight
	end
	
	local team1Rating, team1RD = calculateTeamRating(team1Players)
	local team2Rating, team2RD = calculateTeamRating(team2Players)
	
	-- Update each player's rating against the opposing team
	local newTeam1Players = {}
	for _, player in ipairs(team1Players) do
		local newRating, newRD, newVolatility = RatingService.UpdateRating(
			player.rating, player.rd, player.volatility,
			team2Rating, team2RD, team1Score
		)
		table.insert(newTeam1Players, {
			rating = newRating,
			rd = newRD,
			volatility = newVolatility
		})
	end
	
	local newTeam2Players = {}
	for _, player in ipairs(team2Players) do
		local newRating, newRD, newVolatility = RatingService.UpdateRating(
			player.rating, player.rd, player.volatility,
			team1Rating, team1RD, 1 - team1Score
		)
		table.insert(newTeam2Players, {
			rating = newRating,
			rd = newRD,
			volatility = newVolatility
		})
	end
	
	return newTeam1Players, newTeam2Players
end

-- Calculate MMR change for match result
function RatingService.CalculateMatchDelta(matchResult: Types.MatchResult): {[number]: number}
	local mmrDeltas = {}
	
	if matchResult.Mode == "Ranked1v1" then
		-- 1v1 match
		local player1 = matchResult.Players[1]
		local player2 = matchResult.Players[2]
		
		if not player1 or not player2 then
			return mmrDeltas
		end
		
		-- Determine winner
		local score1 = 0
		local score2 = 0
		for _, team in ipairs(matchResult.Teams) do
			if team.TeamId == player1.Team then
				score1 = team.Score
			else
				score2 = team.Score
			end
		end
		
		local player1Score = 0.5 -- Default to draw
		if score1 > score2 then
			player1Score = 1 -- Player 1 wins
		elseif score2 > score1 then
			player1Score = 0 -- Player 1 loses
		end
		
		-- Update ratings
		local newRating1, _, _ = RatingService.UpdateRating(
			player1.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING,
			RankConfig.MMR_CONFIG.DEFAULT_DEVIATION,
			RankConfig.MMR_CONFIG.DEFAULT_VOLATILITY,
			player2.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING,
			RankConfig.MMR_CONFIG.DEFAULT_DEVIATION,
			player1Score
		)
		
		local newRating2, _, _ = RatingService.UpdateRating(
			player2.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING,
			RankConfig.MMR_CONFIG.DEFAULT_DEVIATION,
			RankConfig.MMR_CONFIG.DEFAULT_VOLATILITY,
			player1.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING,
			RankConfig.MMR_CONFIG.DEFAULT_DEVIATION,
			1 - player1Score
		)
		
		mmrDeltas[player1.UserId] = newRating1 - (player1.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING)
		mmrDeltas[player2.UserId] = newRating2 - (player2.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING)
		
	elseif matchResult.Mode == "Ranked2v2" then
		-- 2v2 team match
		local team1Players = {}
		local team2Players = {}
		
		for _, player in ipairs(matchResult.Players) do
			local playerData = {
				rating = player.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING,
				rd = RankConfig.MMR_CONFIG.DEFAULT_DEVIATION,
				volatility = RankConfig.MMR_CONFIG.DEFAULT_VOLATILITY
			}
			
			if player.Team == 1 then
				table.insert(team1Players, playerData)
			else
				table.insert(team2Players, playerData)
			end
		end
		
		-- Determine team scores
		local team1Score = 0
		local team2Score = 0
		for _, team in ipairs(matchResult.Teams) do
			if team.TeamId == 1 then
				team1Score = team.Score
			else
				team2Score = team.Score
			end
		end
		
		local matchScore = 0.5 -- Default to draw
		if team1Score > team2Score then
			matchScore = 1 -- Team 1 wins
		elseif team2Score > team1Score then
			matchScore = 0 -- Team 1 loses
		end
		
		-- Update team ratings
		local newTeam1Players, newTeam2Players = RatingService.UpdateTeamRating(
			team1Players, team2Players, matchScore
		)
		
		-- Calculate deltas
		local playerIndex = 1
		for _, player in ipairs(matchResult.Players) do
			if player.Team == 1 then
				mmrDeltas[player.UserId] = newTeam1Players[playerIndex].rating - 
					(player.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING)
				playerIndex += 1
			end
		end
		
		playerIndex = 1
		for _, player in ipairs(matchResult.Players) do
			if player.Team == 2 then
				mmrDeltas[player.UserId] = newTeam2Players[playerIndex].rating - 
					(player.MMRBefore or RankConfig.MMR_CONFIG.DEFAULT_RATING)
				playerIndex += 1
			end
		end
	end
	
	return mmrDeltas
end

-- Apply rating decay for inactive players
function RatingService.ApplyRatingDecay(
	rating: number,
	rd: number,
	volatility: number,
	daysSinceLastGame: number
): (number, number, number)
	
	if not RankConfig.MMR_CONFIG.DECAY_ENABLED then
		return rating, rd, volatility
	end
	
	if daysSinceLastGame <= RankConfig.MMR_CONFIG.DECAY_DAYS then
		return rating, rd, volatility
	end
	
	-- Don't decay below minimum rating
	if rating <= RankConfig.MMR_CONFIG.MIN_DECAY_RATING then
		return rating, rd, volatility
	end
	
	local decayDays = daysSinceLastGame - RankConfig.MMR_CONFIG.DECAY_DAYS
	local decayAmount = rating * RankConfig.MMR_CONFIG.DECAY_RATE * decayDays
	
	local newRating = math.max(
		RankConfig.MMR_CONFIG.MIN_DECAY_RATING,
		rating - decayAmount
	)
	
	-- Increase uncertainty (RD) due to inactivity
	local newRD = math.min(
		RankConfig.MMR_CONFIG.MAX_DEVIATION,
		rd + (decayDays * 2) -- Increase RD by 2 per day of inactivity
	)
	
	return newRating, newRD, volatility
end

-- Get expected win probability
function RatingService.GetWinProbability(
	playerRating: number,
	playerRD: number,
	opponentRating: number,
	opponentRD: number
): number
	
	local mu1, phi1 = toGlicko2(playerRating, playerRD)
	local mu2, phi2 = toGlicko2(opponentRating, opponentRD)
	
	return E(mu1, mu2, phi2)
end

-- Calculate confidence interval for rating
function RatingService.GetRatingConfidenceInterval(
	rating: number,
	rd: number,
	confidenceLevel: number?
): (number, number)
	
	local confidence = confidenceLevel or 0.95
	local zScore = 1.96 -- 95% confidence interval
	
	if confidence == 0.99 then
		zScore = 2.576
	elseif confidence == 0.90 then
		zScore = 1.645
	end
	
	local margin = zScore * rd
	return rating - margin, rating + margin
end

return RatingService
